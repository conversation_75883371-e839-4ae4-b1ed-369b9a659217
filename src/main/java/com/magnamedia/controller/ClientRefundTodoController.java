package com.magnamedia.controller;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Iterables;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.EnableSwaggerMethod;
import com.magnamedia.core.annotation.JwtSecured;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.annotation.caching.ApiCacheable;
import com.magnamedia.core.controller.workflow.WorkflowController;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.Template;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.core.repository.TemplateRepository;
import com.magnamedia.core.type.template.ChannelSpecificSettingType;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.entity.workflow.PaymentRequestPurpose;
import com.magnamedia.extra.FilterItem;
import com.magnamedia.extra.MvNotificationTemplateCode;
import com.magnamedia.extra.StreamsUtil;
import com.magnamedia.extra.annotations.UsedBy;
import com.magnamedia.helper.CsvHelper;
import com.magnamedia.helper.UserHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.*;
import com.magnamedia.service.ClientRefundService;
import com.magnamedia.service.OneMonthAgreementFlowService;
import com.magnamedia.workflow.entity.projection.ClientRefundFlowCSVProjection;
import com.magnamedia.workflow.entity.projection.ClientRefundFlowProjection;
import com.magnamedia.workflow.service.ClientRefundFlow;
import com.magnamedia.workflow.type.*;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.*;
import java.util.logging.Level;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Ketrawi
 *         Created on Dec 06, 2020
 *         ACC-2847
 */
@RestController
@RequestMapping("/clientRefundTodo")
public class ClientRefundTodoController extends WorkflowController<ClientRefundToDo, ClientRefundFlow> {

    public static final String CLIENT_REFUND_TODO_ALLOW_EDIT = "client_refund_todo_allow_edit";

    @Autowired
    private ClientRefundTodoRepository clientRefundTodoRepository;

    @Autowired
    DirectDebitRepository directDebitRepository;

    @Autowired
    ContractRepository contractRepository;

    @Autowired
    ClientRepository clientRepository;

    @Autowired
    private ProjectionFactory projectionFactory;

    @Autowired
    private ClientRefundTodoController selfCtrl;

    @Autowired
    private ClientRefundService clientRefundService;

    @Autowired
    private PaymentRepository paymentRepository;

    @Autowired
    private PaymentRequestPurposeRepository paymentRequestPurposeRepository;

    @Override
    public BaseRepository<ClientRefundToDo> getRepository() {
        return clientRefundTodoRepository;
    }

    @Override
    public ResponseEntity<?> createEntity(ClientRefundToDo clientRefundToDo) {

        this.validateAndPrepareEntity(clientRefundToDo);
        ResponseEntity<?> r = super.createEntity(clientRefundToDo);

        // Create PayrollTodo or ConfirmationTodo
        clientRefundService.createTodoUponAutoApprove((ClientRefundToDo) r.getBody());

        // ACC-5663
        if (clientRefundToDo.getParent() == null)
            clientRefundService.sendNotificationToClient(clientRefundToDo);

        return r;
    }

    public void validateAndPrepareEntity(ClientRefundToDo clientRefundToDo) {

        if (clientRefundToDo.isConditionalRefund()) {
            clientRefundService.validateConditionalRefund(clientRefundToDo);
        }

        if (clientRefundToDo.getPurpose() == null) throw new RuntimeException("Purpose should not be empty!");

        if (clientRefundToDo.getContract() == null) throw new RuntimeException("Contract should not be empty!");
        Contract contract = contractRepository.findOne(clientRefundToDo.getContract().getId());

        if (contract.isMaidVisa() && !clientRefundToDo.isAutomaticRefund() && !UserHelper.hasAddRefundToClientPosition() ) {
            throw new BusinessException("You are not authorized to issue refunds for MV contracts!");
        }

        if (clientRefundToDo.getClient() == null) throw new RuntimeException("Client should not be empty!");

        PaymentRequestPurpose purpose = Setup.getRepository(PaymentRequestPurposeRepository.class)
                .findOne(clientRefundToDo.getPurpose().getId());

        ClientRefundSetup setup = purpose.getUniquePurposeSetup(
                clientRefundToDo.getPartialRefundForCancellationPaymentMethod() == null ?
                        null : clientRefundToDo.getPartialRefundForCancellationPaymentMethod().getId());

        if (setup == null) throw new RuntimeException("This purpose don't have setup!");

        if (setup.getValidateClientBank() &&
                !clientRefundToDo.getMethodOfPayment().equals(ClientRefundPaymentMethod.CREDIT_CARD)) {
            if (!directDebitRepository
                    .existDirectDebitByClientAndBanks
                            (clientRefundToDo.getClient().getId(), setup.getBanks()))
                throw new RuntimeException("This client doesn't have any dds related to purpose setup banks");

        }

        if (purpose.getName().equals(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PAYMENT_REQUEST_PURPOSE_PARTIAL_REFUND))) {
            if (!contract.getStatus().equals(ContractStatus.CANCELLED)
                    && !contract.getStatus().equals(ContractStatus.EXPIRED)) {
                if ((contract.getIsScheduledForTermination() == null || !contract.getIsScheduledForTermination()) && contract.getFreezingDate() == null) {
                    throw new RuntimeException("The contract still active you cannot add a refund type with purpose \"Partial refunds - for cancelation\"");
                }
            }

        }


        // ACC-7120
        if (purpose.getName().equals(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_PAYMENT_REQUEST_PURPOSE_MAID_NOT_FINISHING_MEDICAL_STEP)) &&
                clientRefundToDo.getHousemaidPayrollLogId() != null &&
                clientRefundToDo.getPendingSalaryRecord() != null &&
                clientRefundTodoRepository.existsForAcc7120(clientRefundToDo.getContract().getId(), null,
                    clientRefundToDo.getHousemaidPayrollLogId(),
                    java.sql.Date.valueOf(new LocalDate(clientRefundToDo.getPendingSalaryRecord().getTime()).dayOfMonth()
                            .withMinimumValue().toString("yyyy-MM-dd")),
                    java.sql.Date.valueOf(new LocalDate(clientRefundToDo.getPendingSalaryRecord().getTime()).dayOfMonth()
                            .withMaximumValue().toString("yyyy-MM-dd")),
                    purpose.getId(),
                    Arrays.asList(ClientRefundStatus.PAID, ClientRefundStatus.PENDING, ClientRefundStatus.STOPPED))) {

                throw new RuntimeException("There is another refund for the client within the same month.");
        }

        List<User> requestedUsers = setup.getRequestedBy();
        boolean requestedUserExist = false;
        User creator = CurrentRequest.getUser();
        if (!clientRefundToDo.isIgnoreRequestedByConstraint() &&
                creator != null && (requestedUsers != null && requestedUsers.size() > 0)) {
            requestedUserExist = requestedUsers.stream()
                    .anyMatch(user -> user.getId().equals(creator.getId()));
            if (!requestedUserExist) throw new RuntimeException("User can't request this refund purpose");
        }

        if (setup.getAllowMonthlyRefunds() == null || !setup.getAllowMonthlyRefunds())
            clientRefundToDo.setNumberOfMonthlyPayments(null);

        if (!setup.getLinkComplaint()) clientRefundToDo.setComplaint(null);

        if (clientRefundToDo.getMethodOfPayment().equals(ClientRefundPaymentMethod.MONEY_TRANSFER)) {
            clientRefundToDo.setIban(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_CLIENT_REFUND_AGENCY_IBAN));
            clientRefundToDo.setAccountName(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_CLIENT_REFUND_AGENCY_ACCOUNT_NAME));
        }

        clientRefundToDo.setStatus(ClientRefundStatus.PENDING);
        clientRefundToDo.setStatusChangeDate(new java.sql.Date(new java.util.Date().getTime()));

        Client client = Setup.getRepository(ClientRepository.class).findOne(clientRefundToDo.getClient().getId());
        clientRefundToDo.setDescription("Refund for " + client.getName());

        if (clientRefundToDo.getRequestType() == null) {
            clientRefundToDo.setRequestType(ClientRefundRequestType.ERP);
        }

        if (!purpose.getName().equals(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PAYMENT_REQUEST_PURPOSE_PARTIAL_REFUND))) {
            clientRefundToDo.setPartialRefundForCancellationPaymentMethod(null);
        }

        clientRefundToDo.setTaskName(this.getInitialTaskName(clientRefundToDo, setup));
        // ACC-6805
        /*//ACC-4715
        if (clientRefundToDo.isAutomaticRefund() &&
                contract.getActiveContractPaymentTerm().getContract().isPayingViaCreditCard()) {
            clientRefundToDo.setStatus(ClientRefundStatus.STOPPED);
            clientRefundService.markPayrollAsPaidAndRefunded(clientRefundToDo);
        }*/
    }

    private String getInitialTaskName(ClientRefundToDo clientRefundToDo, ClientRefundSetup setup) {

        User currentUser = CurrentRequest.getUser();

        ClientRefundTodoType initialTaskName;
        if (clientRefundToDo.getMethodOfPayment().equals(ClientRefundPaymentMethod.CREDIT_CARD) &&
                (clientRefundToDo.getTransferReference() == null || clientRefundToDo.getRelatedPaymentId() == null ||
                        clientRefundToDo.isAutomaticRefund())) {
            return clientRefundToDo.isConditionalRefund() ?
                    ClientRefundTodoType.CONDITIONAL_REFUND_WAITING_PAYMENTS_BEFORE_MANAGER_APPROVAL.toString() :
                    ClientRefundTodoType.WAITING_MANAGER_APPROVAL.toString();
        }

        if (setup.getAutoApproved()) {
            if (setup.doesNeedCeoApproval(clientRefundToDo.getAmount())) {
                initialTaskName = clientRefundToDo.getMethodOfPayment().equals(ClientRefundPaymentMethod.BANK_TRANSFER) ?
                        ClientRefundTodoType.WAITING_COO_APPROVAL :
                        ClientRefundTodoType.WAITING_MANAGER_APPROVAL;
            } else {
                initialTaskName = clientRefundToDo.getMethodOfPayment().equals(ClientRefundPaymentMethod.CREDIT_CARD) ?
                        ClientRefundTodoType.CREDIT_CARD_TRANSFER_CREATED :
                        ClientRefundTodoType.BANK_TRANSFER_CREATED;
            }
        } else if (!clientRefundToDo.isAutomaticRefund() && currentUser != null &&
                currentUser.getId().equals(setup.getApprovedBy().getId())) {
            if (setup.doesNeedCeoApproval(clientRefundToDo.getAmount())) {
                initialTaskName = clientRefundToDo.getMethodOfPayment().equals(ClientRefundPaymentMethod.BANK_TRANSFER) ?
                        ClientRefundTodoType.WAITING_COO_APPROVAL :
                        ClientRefundTodoType.WAITING_MANAGER_APPROVAL;
            } else {
                initialTaskName = clientRefundToDo.getMethodOfPayment().equals(ClientRefundPaymentMethod.CREDIT_CARD) ?
                        ClientRefundTodoType.CREDIT_CARD_TRANSFER_CREATED :
                        ClientRefundTodoType.BANK_TRANSFER_CREATED;
            }
        } else {

            initialTaskName = UserHelper.hasFamilyRefundCooPosition(setup.getApprovedBy()) &&
                clientRefundToDo.getMethodOfPayment().equals(ClientRefundPaymentMethod.BANK_TRANSFER) ?
                                    ClientRefundTodoType.WAITING_COO_APPROVAL :
                                    ClientRefundTodoType.WAITING_MANAGER_APPROVAL;
        }

        if (clientRefundToDo.isConditionalRefund()) {
            switch (initialTaskName) {
                case WAITING_COO_APPROVAL:
                    initialTaskName = ClientRefundTodoType.CONDITIONAL_REFUND_WAITING_PAYMENTS_BEFORE_COO_APPROVAL;
                    break;
                case WAITING_MANAGER_APPROVAL:
                    initialTaskName = ClientRefundTodoType.CONDITIONAL_REFUND_WAITING_PAYMENTS_BEFORE_MANAGER_APPROVAL;
                    break;
            }
        }

        return initialTaskName.toString();
    }

    @Override
    public ResponseEntity<?> updateEntity(ClientRefundToDo entity) {

        if (Arrays.asList(
                ClientRefundTodoType.CONDITIONAL_REFUND_WAITING_PAYMENTS_BEFORE_MANAGER_APPROVAL.toString(),
                        ClientRefundTodoType.CONDITIONAL_REFUND_WAITING_PAYMENTS_BEFORE_COO_APPROVAL.toString())
                .contains(entity.getTaskName()) &&
                (!entity.isConditionalRefund() || entity.getRequiredPayments().isEmpty() ||
                 Setup.getRepository(PaymentRepository.class)
                         .findAll(entity.getRequiredPayments().stream().map(Payment::getId).collect(Collectors.toList()))
                        .stream()
                        .allMatch(p -> p.getStatus().equals(PaymentStatus.RECEIVED) || p.isReplaced()))) {

            entity.setTaskName(
                    entity.getTaskName()
                            .equals(ClientRefundTodoType.CONDITIONAL_REFUND_WAITING_PAYMENTS_BEFORE_COO_APPROVAL.toString()) ?
                            ClientRefundTodoType.WAITING_COO_APPROVAL.toString() :
                            ClientRefundTodoType.WAITING_MANAGER_APPROVAL.toString());
        }

        return super.updateEntity(entity);
    }

    //ACC-2882
    @PreAuthorize("hasPermission('clientRefundTodo','search')")
    @RequestMapping(value = "/search/page",
            method = RequestMethod.POST)
    public ResponseEntity<?> search(
            Pageable pageable,
            @RequestBody List<FilterItem> filters) {

        SelectQuery<ClientRefundToDo> query = new SelectQuery(ClientRefundToDo.class);

        // Process Filters
        SelectFilter selectFilter = new SelectFilter();
        for (FilterItem filter : filters) {
            selectFilter = selectFilter.and(filter.getSelectFilter(ClientRefundToDo.class));
        }

        query.filterBy(selectFilter);
        //ACC-4715
        query.filterBy("status", "!=", ClientRefundStatus.STOPPED);
        //Sorting
        if (Iterables.isEmpty(pageable.getSort())) {
            query.sortBy("lastModificationDate", false, true);
        }
        
        return new ResponseEntity<>(
                query.execute(pageable).map(
                        obj -> projectionFactory.createProjection(
                                getProjectionClass(), obj)),
                HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('clientRefundTodo','downloadSearchAttachment')")
    @RequestMapping(value = "/downloadSearchAttachment/csv",
            method = RequestMethod.POST)
    public void downloadSearchAttachment(Sort sort,
                                         @RequestParam(name = "limit", required = false, defaultValue = "1000") Integer limit,
                                         @RequestBody List<FilterItem> filters, HttpServletResponse response) {

        SelectQuery<ClientRefundToDo> query = new SelectQuery(ClientRefundToDo.class);
        SelectFilter selectFilter = new SelectFilter();

        for (FilterItem filter : filters) {
            selectFilter = selectFilter.and(filter.getSelectFilter(ClientRefundToDo.class));
        }

        query.filterBy(selectFilter);
        //ACC-4715
        query.filterBy("status", "!=", ClientRefundStatus.STOPPED);
    
        if (sort == null) {
            query.sortBy("statusChangeDate", false, true);
        } else {
            for (Sort.Order order : sort) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        }

        InputStream inputStream = null;
        File excelFile;
        try {

            query.setLimit(limit);
            List<ClientRefundToDo> clientRefundToDo = query.execute();

            String[] namesOrdered = {"Name", "Category", "Purpose", "Amount", "MasterAmount", "Status", "Lenient",
                    "LeniencyType", "ContractType", "Date", "Notes", "RequestID", "Transaction", "RequestedBy",
                    "ComplaintType", "Attachments"};

            String[] headers =
                    {"Client Name", "Category", "Purpose",
                            "Amount", "Master Amount", "Status", "Is Lenient", "Leniency Type", "Contract Type",
                            "Date", "Notes", "Request ID", "Transaction", "Requested By", "Complaint Type","Attachments"};


                excelFile = CsvHelper.generateCsv(clientRefundToDo, ClientRefundFlowCSVProjection.class, headers, namesOrdered,
                        "ClientRefundSummary.csv");

        } catch (IOException ex) {
            logger.log(Level.SEVERE, ex.getMessage(), ex);
            throw new RuntimeException(ex.getMessage());
        }

        try {
            inputStream = new FileInputStream(excelFile);
            if (inputStream != null) {
                createDownloadResponse(response, "ClientRefundSummary.csv", inputStream);
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } finally {
            StreamsUtil.closeStream(inputStream);
        }
    }

    //ACC-2882
    @PreAuthorize("hasPermission('clientRefundTodo','getClientRefunds')")
    @RequestMapping(value = "/getClientRefunds/page", method = RequestMethod.GET)
    public ResponseEntity<?> getClientRefunds(
            Pageable pageable,
            Sort sort,
            @RequestParam(value = "clientId") Long clientId,
            @RequestParam(value = "contractId") Long contractId) {

        Client client = clientRepository.findOne(clientId);

        Contract contract = contractRepository.findOne(contractId);

        if (client == null || contract == null)
            throw new RuntimeException("No Client or Contract is Selected!");

        return new ResponseEntity<>(
                clientRefundTodoRepository.findByClientAndContract(client, contract, pageable)
                        .map(obj -> projectionFactory.createProjection(getProjectionClass(), obj)),
                HttpStatus.OK);
    }

    // ACC-9043
    @JwtSecured
    @ApiCacheable
    @EnableSwaggerMethod
    @UsedBy(others = UsedBy.Others.New_GPT)
    @PreAuthorize("hasPermission('clientRefundTodo','getRefundReportGPT')")
    @GetMapping("/getRefundReportGPT")
    public ResponseEntity<?> getRefundReportGPT(@RequestParam Long contractId) {
        Contract contract = contractRepository.findOne(contractId);
        if (contract == null) {
            throw new BusinessException("Contract not found");
        }

        List<Map<String, Object>> refunds = clientRefundTodoRepository.findRefundInfoByContract(contract);

        String refundReport = refunds.stream()
            .map(refund -> String.format("client has a refund of %s with method %s and status %s on %s with notes %s",
                ((Double) refund.get("amount")).longValue(),
                ((ClientRefundPaymentMethod) refund.get("methodOfPayment")).getLabel(),
                ((ClientRefundStatus) refund.get("status")).getLabel(),
                refund.get("date"),
                refund.get("notes") != null ? refund.get("notes") : ""))
            .collect(Collectors.joining(","));

        return ResponseEntity.ok(new HashMap<String, String>() {{
            put("refundReport", refundReport);
        }});
    }

    //ACC-2849
    @PreAuthorize("hasPermission('clientRefundTodo','getClientRefundsPreviousRequests')")
    @RequestMapping(value = "/getClientRefundsPreviousRequests", method = RequestMethod.GET)
    public ResponseEntity<?> getClientRefundsPreviousRequests(
            @RequestParam(value = "clientId", required = true) Long clientId,
            @RequestParam(value = "requestId", required = true) Long requestId) {

        Client client = clientRepository.findOne(clientId);

        if (client == null)
            throw new RuntimeException("No client is found.");

        ClientRefundToDo clientRefundToDo = clientRefundTodoRepository.findOne(requestId);

        if (clientRefundToDo == null)
            throw new RuntimeException("No request is found.");

        SelectQuery<ClientRefundToDo> query = new SelectQuery<>(ClientRefundToDo.class);
        query.filterBy("client", "=", client);
        query.filterBy("id", "<>", clientRefundToDo.getId());
        //ACC-4715
        query.filterBy("status", "!=", ClientRefundStatus.STOPPED);
        //query.filterBy("creationDate","<",clientRefundToDo.getCreationDate());
        query.sortBy("creationDate", false);

        List<ClientRefundFlowProjection> clientRefundToDos = (List<ClientRefundFlowProjection>) query.execute().stream().map(
                        obj -> projectionFactory.createProjection(getProjectionClass(), obj))
                .collect(Collectors.toList());

        return new ResponseEntity<>(new HashMap<String, Object>() {{
            put("previousRequestsRelatedToSameContract", clientRefundToDos.stream()
                    .filter(c -> c.getContract().getId().equals(clientRefundToDo.getContract().getId())).collect(Collectors.toList()));
            put("previousRequestsRelatedToOtherContract", clientRefundToDos.stream()
                    .filter(c -> !c.getContract().getId().equals(clientRefundToDo.getContract().getId())).collect(Collectors.toList()));
        }}, HttpStatus.OK);
    }

    //ACC-8487
    @UsedBy(others = UsedBy.Others.New_GPT)
    @ApiCacheable
    @EnableSwaggerMethod
    @PreAuthorize("hasPermission('clientRefundTodo','getClientRefundPaymentsSummary')")
    @GetMapping("/getClientRefundPaymentsSummary")
    public ResponseEntity<?> getClientRefundPaymentsSummary(@RequestParam(name = "contractID") Contract contract) {
        HashMap<String, Object> result = new HashMap<>();

        // clientHasPdgRefund
        Double amountSum = clientRefundTodoRepository.findPendingRefundAmountByContract(contract, ClientRefundStatus.PENDING);
        result.put("clientHasPdgRefund", amountSum > 0);

        // pdgRefundAmount
        result.put("pdgRefundAmount", String.valueOf(amountSum.longValue()));

        // currentMonthPayments
        OneMonthAgreementFlowService oneMonthAgreementFlowService = Setup.getApplicationContext()
                .getBean(OneMonthAgreementFlowService.class);

        Date startDate = new LocalDate().dayOfMonth().withMinimumValue().toDate();
        Date endDate = new LocalDate().dayOfMonth().withMaximumValue().toDate();
        if (oneMonthAgreementFlowService.isPayingViaCreditCard(contract)) {
            startDate = oneMonthAgreementFlowService.getCurrentPaymentDate(contract).toDate();
            endDate = oneMonthAgreementFlowService.getLastDayInCurrentPayment(contract).toDate();
        }
        List<Object[]> currentMonthPayments = paymentRepository.findCurrentMonthPaymentsInfo(contract, startDate, endDate);

        StringBuilder currentMonthPaymentsString = new StringBuilder();

        for (int i = 0; i < currentMonthPayments.size(); i++) {
            Object[] row = currentMonthPayments.get(i);

            currentMonthPaymentsString.append(i == 0 ? row[2].toString() : row[2].toString().toLowerCase())
                    .append(" totaling ")
                    .append(String.format("%,.0f", (Double) row[1])).append(" AED through ")
                    .append(((PaymentMethod) row[3]).getLabel().toLowerCase()).append(" is ")
                    .append(PaymentStatus.PDC.equals(row[0]) ? "pending" : ((PaymentStatus) row[0]).getLabel().toLowerCase());

            if (i < currentMonthPayments.size() - 1) {
                currentMonthPaymentsString.append(", ");
            }
        }
        if (currentMonthPayments.size() > 0) {
            currentMonthPaymentsString.append(".");
        }
        result.put("currentMonthPayments", currentMonthPaymentsString);
        return ResponseEntity.ok(result);
    }

    @PreAuthorize("hasPermission('clientRefundTodo','clientAccountInfo')")
    @GetMapping(value = "/clientAccountInfo/{id}")
    public ResponseEntity<?> getClientAccountInfo(@PathVariable("id") Client client) {
        if (client == null) throw new RuntimeException("No Client is found!");

        List<Object[]> o = directDebitRepository.getClientAccountInfo(client.getId());
        Map<String, Object> r = new HashMap<>();

        if (o.isEmpty()) {
            r.put("accountName", "");
            r.put("iban", "");
            r.put("NoConfirmedDD", "True");
            r.put("bankName", "");
            return new ResponseEntity<>(r, HttpStatus.OK);
        }

        logger.info(Arrays.stream(o.get(0)).map(e -> e.toString()).collect(Collectors.joining(", ")));

        PicklistItem item = Setup.getItem("BankName", (String) o.get(0)[3]);
        r.put("selectedId", o.get(0)[0]);
        r.put("accountName", o.get(0)[1]);
        r.put("iban", o.get(0)[2]);
        r.put("bankName", item.hasTag("IBAN_BANK_NAME") ? item.getTagValue("IBAN_BANK_NAME").getValue() : item.getName());
        return new ResponseEntity<>(r, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('clientRefundTodo','getCeoScreenInfo')")
    @GetMapping(value = "/getCeoScreenInfo")
    public ResponseEntity<?> getCeoScreenInfoAPI() {
        return new ResponseEntity(getCeoScreenInfo(), HttpStatus.OK);
    }

    public Map getCeoScreenInfo() {
        List<ClientRefundToDo> data = clientRefundTodoRepository
                .findByTaskNameAndCeoActionAndStatus(ClientRefundTodoType.WAITING_COO_APPROVAL.toString(), null, ClientRefundStatus.PENDING);
        Double sum = data.stream().map(ClientRefundToDo::getAmount).mapToDouble(Double::doubleValue).sum();

        Map<String, Object> result = new HashMap<>();
        result.put("count", data.size());
        result.put("amount", sum);

        return result;
    }

    @PreAuthorize("hasPermission('clientRefundTodo','getCeoPendingToDos')")
    @GetMapping(value = "/getCeoPendingToDos")
    @Transactional
    public ResponseEntity getCeoPendingToDosAPI(
            @RequestParam("questionedPage") CooQuestion.QuestionedPage questionedPage,
            Pageable pageable) {

        return ResponseEntity.ok(
                clientRefundTodoRepository.findByTaskNameAndCeoActionAndStatus(
                        ClientRefundTodoType.WAITING_COO_APPROVAL.toString(), null, ClientRefundStatus.PENDING, pageable)
                        .map(clientRefund -> {
                            clientRefund.setCooQuestionedPage(questionedPage);
                            return projectionFactory.createProjection(ClientRefundFlowProjection.class, clientRefund);
                        }));
    }

    @PreAuthorize("hasPermission('clientRefundTodo','ceoAction/approveAll')")
    @PostMapping(value = "/ceoAction/approveAll")
    public ResponseEntity ceoActionApproveAllAPI() throws IOException {
        List<ClientRefundToDo> toDos = clientRefundTodoRepository.findByTaskNameAndCeoActionAndStatus(
                ClientRefundTodoType.WAITING_COO_APPROVAL.toString(), null, ClientRefundStatus.PENDING);

        for (ClientRefundToDo toDo : toDos) {
            selfCtrl.approve(toDo.getId());
        }

        return okResponse();
    }

    @PreAuthorize("hasPermission('clientRefundTodo','ceoAction/approveList')")
    @RequestMapping(value = "/ceoAction/approveList", method = RequestMethod.POST)
    public ResponseEntity ceoActionApproveListAPI(@RequestBody List<Long> toDosIDs) throws IOException {
        for (Long toDoId : toDosIDs) {
            selfCtrl.approve(toDoId);
        }

        return okResponse();
    }

    @PreAuthorize("hasPermission('clientRefundTodo','getClientRefundInfo')")
    @GetMapping(value = "/getClientRefundInfo/{id}")
    public ResponseEntity getClientRefundInfo(@PathVariable("id") ClientRefundToDo clientRefundToDo)  {

        Map<String, Object> result = new HashMap<>();
        result.put("clientRefundTodoAllowEdit", CurrentRequest.getUser() != null && CurrentRequest.getUser().hasPosition(CLIENT_REFUND_TODO_ALLOW_EDIT));

        // ACC-7699
        result.put("contractInfo", new HashMap<String, Object>() {{
            Contract contract = clientRefundToDo.getContract();
            put("id", contract.getId());
            put("contractType", contract.getContractProspectType().getName());
            put("status", contract.getStatus());
            put("startOfContract", new LocalDate(contract.getStartOfContract()).toString("yyyy-MM-dd"));
            put("paidEndDate", new LocalDate(contract.getPaidEndDate()).toString("yyyy-MM-dd"));
            put("scheduledDateOfTermination", contract.getScheduledDateOfTermination());
            put("dateOfTermination", contract.getDateOfTermination() != null ?
                    new LocalDate(contract.getDateOfTermination()).toString("yyyy-MM-dd") : "");

            Date d = contract.isEnded() && contract.getDateOfTermination() != null ?
                    contract.getDateOfTermination() :
                    new Date();
            put("payments", Setup.getRepository(PaymentRepository.class)
                    .findAllPaymentsByContractAndBetweenTwoDate(contract.getId(),
                    new LocalDate(d).dayOfMonth().withMinimumValue().toDate(),
                    new LocalDate(d).dayOfMonth().withMaximumValue().toDate())
                    .stream().map(payment -> {
                        payment.put("status", clientRefundService.getPaymentStatus(payment));
                        payment.remove("replaced");
                        return payment;
                    }).collect(Collectors.toList()));
        }});

        result.put("conditionalRefundPaymentInfo", clientRefundToDo.isConditionalRefund() ?
                clientRefundToDo.getRequiredPayments()
                        .stream()
                        .map(p -> new HashMap<String, Object>() {{
                            put("id", p.getId());
                            put("dateOfPayment", p.getDateOfPayment());
                            put("amountOfPayment", p.getAmountOfPayment());
                            put("status", clientRefundService
                                    .getPaymentStatus(new HashMap<String, Object>() {{
                                        put("status", p.getStatus());
                                        put("replaced", p.getReplaced());
                                    }}));
                            put("typeOfPayment", p.getTypeOfPayment().getName());
                            put("methodOfPayment", p.getMethodOfPayment());
                        }})
                        .collect(Collectors.toList()) :
                "");

        return ResponseEntity.ok(result);
    }

    @PreAuthorize("hasPermission('clientRefundTodo','updateConditionalRefundTodo')")
    @PostMapping("/updateConditionalRefundTodo")
    public ResponseEntity<?> updateConditionalRefundTodo(@RequestBody ObjectNode objectNode) throws IOException {

        ClientRefundToDo oldTodo = clientRefundTodoRepository.findOne(getObjectMapper().convertValue(objectNode.get("id"), Long.class));
        if (oldTodo == null || oldTodo.getTaskName() == null ||
                !Arrays.asList(
                        ClientRefundTodoType.CONDITIONAL_REFUND_WAITING_PAYMENTS_BEFORE_MANAGER_APPROVAL.toString(),
                                ClientRefundTodoType.CONDITIONAL_REFUND_WAITING_PAYMENTS_BEFORE_COO_APPROVAL.toString())
                        .contains(oldTodo.getTaskName())) {
            throw new BusinessException("The client refund todo cannot be updated");
        }

        return super.update(objectNode);
    }

    @Transactional
    public void approve(Long clientRefundToDoId) throws IOException {
        ClientRefundToDo persistedTodo = getRepository().getOne(clientRefundToDoId);

        ObjectNode jsonNode = new ObjectMapper().createObjectNode();
        jsonNode.put("id", clientRefundToDoId);
        jsonNode.put("ceoAction", ClientRefundTodoManagerAction.APPROVE.toString());

        super.completeTask(persistedTodo, jsonNode, ClientRefundTodoType.WAITING_COO_APPROVAL.toString());
    }

    @Override
    protected SelectFilter filter(SelectFilter filter, String
            search, List<String> joins, Map<String, String> joinType) {

        return filter;
    }

    @Override
    protected Class<?> getProjectionClass() {
        return ClientRefundFlowProjection.class;
    }

    @GetMapping("/getRefundMethods")
    @NoPermission
    public ResponseEntity<?> getRefundMethods(@RequestParam(value = "search", required = false) String search) {

        List<Map<String, String>> l = Arrays.stream(ClientRefundPaymentMethod.values())
                .filter(m -> !m.isDeprecated() &&
                        (search == null || search.isEmpty() ||
                                m.getLabel().toLowerCase().contains(search.toLowerCase())))
                .map(m -> new HashMap<String, String>() {{
                    put("id", m.getValue());
                    put("label", m.getLabel());
                }})
                .collect(Collectors.toList());

        return new ResponseEntity<>(l, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('clientRefundTodo','getAllowRefundAmount')")
    @GetMapping(value = "/getAllowRefundAmount/{refundId}/{selectedPaymentId}")
    public ResponseEntity<?> getAllowRefundAmount(
            @PathVariable("refundId") ClientRefundToDo clientRefundToDo, @PathVariable("selectedPaymentId") Long selectedPaymentId,
            @RequestParam("transferNumber") String transferNumber) {
        Double allowRefundAmount = null;
        try {
            Map<String, Object> m = Setup.getApplicationContext().getBean(ClientRefundService.class)
                    .getReceivedTransactionAndTotalAllowedRefund(clientRefundToDo, transferNumber);
            allowRefundAmount = (Double) m.get("allowRefundAmount");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResponseEntity.ok(allowRefundAmount);
    }

    // ACC-8308
    @JwtSecured
    @EnableSwaggerMethod
    @UsedBy(others = UsedBy.Others.New_GPT)
    @PostMapping(value = "/addNewRefundFromGpt")
    public ResponseEntity<?> addNewRefundFromGpt(@RequestBody Map<String, Object> body) {
        Map<String,Object> result = new HashMap<>();
        try {
            createEntity(clientRefundService.validateAndPrepareClientRefundByGPT(body));
            result.put("requestSuccessful", true);
        } catch (Exception e) {
            result.put("requestSuccessful", false);
            result.put("failureReason", e.getMessage());
        }

        return ResponseEntity.ok(result);
    }


    @PreAuthorize("hasPermission('clientRefundTodo','migrateAcc8384')")
    @GetMapping(value = "/migrateAcc8384")
    public ResponseEntity<?> migrateAcc8384() {
        Template t = Setup.getApplicationContext()
                .getBean(TemplateRepository.class)
                .findByNameIgnoreCase(String.valueOf(MvNotificationTemplateCode.MV_REFUND_SALARY_AMOUNT_E_VISA_NOT_ISSUED_OR_ISSUED_IN_THE_PREVIOUS_MONTH_NOTIFICATION));

        String notificationText = "@greetings@\n" +
                "Since your @worker_type@'s visa is not issued yet, we will refund the @worker_type@'s " +
                "salary to your bank account. If you would like to pay @her_his@ anything " +
                "for @previous_month@, please pay @her_his@ in cash. Your @worker_type@ should begin " +
                "receiving @her_his@ full salary directly from us starting next month. " +
                "You can expect to receive your refund within the next 7 business days. " +
                "Sorry for any inconvenience caused.";

        t.getChannelSetting(ChannelSpecificSettingType.Notification).setText(notificationText);

        TemplateUtil.updateChannelSpecificSettings(t, t.getChannelSpecificSettings());

        return okResponse();
    }

    // MV_HOUSEMAID_REFUND_SALARY_AMOUNT_E_VISA_NOT_ISSUED_OR_ISSUED_IN_THE_PREVIOUS_MONTH_NOTIFICATION
    @PreAuthorize("hasPermission('clientRefundTodo','sendToMaidAndClientMsgAcc8384')")
    @GetMapping(value = "/sendToMaidAndClientMsgAcc8384/{id}")
    public ResponseEntity<?> sendToMaidAndClientMsgAcc8384(@PathVariable("id") Long clientRefundToDoId) {

        clientRefundService.checkEVisaNotIssuedOrIssuedInThePreviousMonth(clientRefundToDoId);

        return ResponseEntity.ok(true);
    }

    @PreAuthorize("hasPermission('clientRefundTodo','checkClientRefundStatusByPurpose')")
    @GetMapping(value = "/checkClientRefundStatusByPurpose")
    public ResponseEntity<?> checkClientRefundStatusByPurpose(
            @RequestParam(name = "contractId") Long contractId,
            @RequestParam(name = "purposeName", required = false, defaultValue = "Contract Start Date Dispute") String purposeName,
            @RequestParam(name = "purposeId", required = false) Long purposeId) {

        // Validate input parameters
        if (contractId == null) {
            return ResponseEntity.badRequest().body("Contract ID is required");
        }

        if ((purposeName == null || purposeName.trim().isEmpty()) && purposeId == null) {
            return ResponseEntity.badRequest().body("Either purposeName or purposeId is required");
        }

        // Find contract
        Contract contract = contractRepository.findOne(contractId);
        if (contract == null) {
            return ResponseEntity.badRequest().body("Contract not found with ID: " + contractId);
        }

        // Find purpose
        PaymentRequestPurpose purpose = null;
        if (purposeId != null) {
            purpose = paymentRequestPurposeRepository.findOne(purposeId);
            if (purpose == null) {
                return ResponseEntity.badRequest().body("Purpose not found with ID: " + purposeId);
            }
        } else {
            purpose = paymentRequestPurposeRepository.findByName(purposeName.trim());
            if (purpose == null) {
                return ResponseEntity.badRequest().body("Purpose not found with name: " + purposeName);
            }
        }

        // Check if refunds exist for this contract and purpose
        boolean hasRefunds = clientRefundTodoRepository.existsByContractAndPurpose(contract.getId(), purpose.getId());

        // Create response with a dynamic key based on purpose name
        String purposeNameForKey = purpose.getName().toLowerCase()
                .replaceAll("[^a-zA-Z0-9]", "_")
                .replaceAll("_+", "_")
                .replaceAll("^_|_$", "");

        String responseKey = purposeNameForKey + "_status";

        return ResponseEntity.ok(new HashMap<String, Object>() {{
            put(responseKey, hasRefunds);
        }});
    }

    @JwtSecured
    @PostMapping(value = "/firstSecondaryPayrollPaidAcc7120")
    public ResponseEntity<?> firstSecondaryPayrollPaidAcc7120(@RequestBody List<Map<String, Object>> body) {
        clientRefundService.firstSecondaryPayrollPaidAcc7120(body);
        return ResponseEntity.ok(true);
    }
}

