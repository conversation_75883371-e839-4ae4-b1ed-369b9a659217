package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.extra.LabelValueEnum;
import com.magnamedia.module.type.PaymentType;
import com.magnamedia.workflow.visa.ExpensePurpose;

import javax.persistence.*;


@Entity
public class VisaExpenseConfiguration extends BaseEntity {

    public enum EmployeeType implements LabelValueEnum {
        MAID_CC("Maids.cc"),
        MAID_VISA("Maidvisa"),
        OFFICE_STAFF("Office staff");

        private final String label;

        EmployeeType(String label) {
            this.label = label;
        }

        public String getLabel() {
            return label;
        }
    }

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private EmployeeType employeeType;

    @Column(columnDefinition = "boolean default false")
    private boolean newEmployee = false;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private ExpensePurpose expensePurpose;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private Expense expense;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private PaymentType paymentType;

    public EmployeeType getEmployeeType() { return employeeType; }

    public void setEmployeeType(EmployeeType employeeType) { this.employeeType = employeeType; }

    public boolean isNewEmployee() { return newEmployee; }

    public void setNewEmployee(boolean newEmployee) { this.newEmployee = newEmployee; }

    public ExpensePurpose getExpensePurpose() { return expensePurpose; }

    public void setExpensePurpose(ExpensePurpose expensePurpose) { this.expensePurpose = expensePurpose; }

    public Expense getExpense() { return expense; }

    public void setExpense(Expense expense) { this.expense = expense; }

    public PaymentType getPaymentType() { return paymentType; }

    public void setPaymentType(PaymentType paymentType) { this.paymentType = paymentType; }
}