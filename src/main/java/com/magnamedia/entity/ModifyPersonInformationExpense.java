package com.magnamedia.entity;

import com.magnamedia.workflow.visa.ExpensePurpose;

import javax.persistence.Entity;

@Entity
public class ModifyPersonInformationExpense extends VisaExpense<ModifyPersonInformationRequest>{
    public ModifyPersonInformationExpense() {
        super(null, null);
    }

    public ModifyPersonInformationExpense(ModifyPersonInformationRequest request, ExpensePurpose purpose) {
        super(request, purpose);
    }

    @Override
    public String getVisaExpenseType() {
        return "ModifyPersonInformationExpense";
    }
}