package com.magnamedia.entity.projection;


import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.User;
import com.magnamedia.module.type.ExpensePaymentMethod;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @created 28/09/2024 - 10:18 AM
 * ACC-
 */
public interface ExpensePaymentSecureInfoProjection {

    Long getId();

    String getAmountWithSecure();

    @Value("#{target.getCurrency() != null ? { id: target.getCurrency().getId(), label : target.getCurrency().getName() } : null }")
    Map<?, ?> getCurrency();

    ExpensePaymentMethod getMethod();

    @Value("#{target.getFromBucket() != null ? { " +
                "id: target.getFromBucket().getId(), " +
                "name : target.getFromBucket().getName(), " +
                "code : target.getFromBucket().getCode() " +
            "} : null }")
    Map<?, ?> getFromBucket();

    Date getCreationDate();

    Double getVatAmount();

    @Value("#{target.getPaidBy() != null ? { id: target.getPaidBy().getId(), label : target.getPaidBy().getLabel() } : null }")
    Map<?, ?> getPaidBy();

    List<Attachment> getAttachments();
}
