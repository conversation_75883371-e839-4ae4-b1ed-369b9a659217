package com.magnamedia.entity.projection;

import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.module.type.TransactionEntityType;
import com.magnamedia.module.type.VatType;
import org.springframework.beans.factory.annotation.Value;

import java.sql.Date;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Aug 19, 2020
 *         Jirra ACC-2394
 */

public interface PLNodeTransactionCSVProjection {
    Long getId();

    java.util.Date getPnlValueDate();

    @Value("#{target.getFromBucket()!=null? target.getFromBucket().getName():''}")
    String getFromBucketName();

    @Value("#{target.getRevenue()!=null? target.getRevenue().getName():''}")
    String getRevenueName();

    @Value("#{target.getExpense()!=null? target.getExpense().getNameLabel():''}")
    String getExpenseName();

    @Value("#{target.getToBucket()!=null? target.getToBucket().getName():''}")
    String getToBucketName();

    String getDescription();

    PaymentMethod getPaymentType();

    Double getAmount();

    Date getDate();

    java.util.Date getCreationDate();

    TransactionEntityType getTransactionType();

    VatType getVatType();

    Double getVatAmount();

    PicklistItem getLicense();

    Double getAverageAmount();

    Double getProfitAdjustment();
}
