package com.magnamedia.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.module.type.PaymentMethod;
import com.magnamedia.module.type.PaymentOrderStatus;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.module.type.PostingEngineEntityType;
import com.magnamedia.workflow.type.PaymentRequestMethod;
import org.springframework.beans.factory.annotation.Value;

import java.util.Map;

/**
 * <AUTHOR> masod <<EMAIL>>
 *         Created on Aug 13, 2020
 *         ACC-2330
 */

public interface TransactionPostingRuleProjection {

    Long getId();

    PostingEngineEntityType getPostingEngineEntityType();

    @JsonSerialize(using = IdLabelCodeSerializer.class)
    PicklistItem getTypeOfPayment();

    PaymentMethod getMethodOfPayment();

    PaymentStatus getPaymentStatus();

    @JsonSerialize(using = IdLabelCodeSerializer.class)
    PicklistItem getCompany();

    PaymentOrderStatus getPaymentOrderStatus();

    PaymentRequestMethod getPaymentOrderMethodOfPayment();

    @Value("#{target.getPurposeOfPayment() != null ? "
            + "{id: target.getPurposeOfPayment().getId(), "
            + "label: target.getPurposeOfPayment().getLabel(),"
            + "name: target.getPurposeOfPayment().getName()}"
            + ": null}")
    Map<?, ?> getPurposeOfPayment();

    @Value("#{target.getRevenue() != null ? "
            + "{id: target.getRevenue().getId(), "
            + "code: target.getRevenue().getCode(),"
            + "name: target.getRevenue().getName()}"
            + ": null}")
    Map<?, ?> getRevenue();

    @Value("#{target.getFromBucket() != null ? "
            + "{id: target.getFromBucket().getId(), "
            + "code: target.getFromBucket().getCode(),"
            + "name: target.getFromBucket().getName()}"
            + ": null}")
    Map<?, ?> getFromBucket();

    @Value("#{target.getToBucket() != null ? "
            + "{id: target.getToBucket().getId(), "
            + "code: target.getToBucket().getCode(),"
            + "name: target.getToBucket().getName()}"
            + ": null}")
    Map<?, ?> getToBucket();

    @Value("#{target.getExpense() != null ? "
            + "{id: target.getExpense().getId(), "
            + "code: target.getExpense().getCode(),"
            + "label: target.getExpense().getLabel(),"
            + "name: target.getExpense().getName()}"
            + ": null}")
    Map<?, ?> getExpense();

    Boolean getActive();
}
