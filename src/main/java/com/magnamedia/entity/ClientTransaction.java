package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.serializer.IdNameMobileSerializer;
import org.hibernate.envers.NotAudited;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Feb 8, 2020
 *         Jirra ACC-1227
 */

@Entity
public class ClientTransaction extends BaseEntity {

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Transaction transaction;

    @NotAudited
    @JsonSerialize(using = IdNameMobileSerializer.class)
    @ManyToOne(fetch = FetchType.LAZY)
    private Client client;


    @Override
    public boolean equals(Object object) {
        if (!(object instanceof ClientTransaction)) {
            return false;
        } else {
            ClientTransaction other = (ClientTransaction) object;
            return this.getId().equals(other.getId())
                    && this.client.getId().equals(other.client.getId()) && this.transaction.getId().equals(other.transaction.getId());
        }
    }

    public ClientTransaction() {
    }

    public ClientTransaction(Transaction transaction, Client client) {
        this.transaction = transaction;
        this.client = client;
    }

    public Transaction getTransaction() {
        return transaction;
    }

    public void setTransaction(Transaction transaction) {
        this.transaction = transaction;
    }

    public Client getClient() {
        return client;
    }

    public void setClient(Client client) {
        this.client = client;
    }
}
