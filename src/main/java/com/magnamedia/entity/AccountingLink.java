package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.helper.Shortener;
import com.magnamedia.repository.AccountingLinkRepository;

import javax.persistence.*;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


@Entity
public class AccountingLink extends BaseEntity {

    public enum AccountingLinkType {
        ONLINE_CARD,
        SIGN_DD_WEB_PAGE
    }

    public enum AccountingLinkStatus {
        PENDING, DONE, EXPIRED
    }

    public enum AccountingLinkExpirationReason {
        NEW_LINK_ADDED
    }

    @Column
    private Long contractId;
    @Column
    private Long cptId;

    @Column
    private String originalLink;
    @Column
    private String ShortenedLink;
    @Column
    @Enumerated(EnumType.STRING)
    private AccountingLinkType type;
    @Column
    @Enumerated(EnumType.STRING)
    private AccountingLinkStatus status = AccountingLinkStatus.PENDING;
    @Column
    private Date doneDate;
    @Column
    private Date expirationDate;
    @Column
    @Enumerated(EnumType.STRING)
    private AccountingLinkExpirationReason expirationReason;

    @Column
    private Long relatedEntityId;
    @Column
    private String relatedEntityType;
    @Column
    private Long relatedFlowId;
    @Column
    private String relatedFlowEntityType;

    @Lob
    private String additionalInfo;

    public Long getContractId() { return contractId; }

    public void setContractId(Long contractId) { this.contractId = contractId; }

    public Long getCptId() { return cptId; }

    public void setCptId(Long cptId) { this.cptId = cptId; }

    public String getOriginalLink() { return originalLink; }

    public void setOriginalLink(String originalLink) { this.originalLink = originalLink; }

    public String getShortenedLink() { return ShortenedLink; }

    public void setShortenedLink(String shortenedLink) { ShortenedLink = shortenedLink; }

    public AccountingLinkType getType() { return type; }

    public void setType(AccountingLinkType type) { this.type = type; }

    public AccountingLinkStatus getStatus() { return status; }

    public void setStatus(AccountingLinkStatus status) {

        switch (status) {
            case EXPIRED:
                setExpirationDate(new Date());
                break;
            case DONE:
                setDoneDate(new Date());
                break;
        }
        this.status = status;
    }

    public Date getDoneDate() { return doneDate; }

    public void setDoneDate(Date doneDate) { this.doneDate = doneDate; }

    public Date getExpirationDate() { return expirationDate; }

    public void setExpirationDate(Date expirationDate) { this.expirationDate = expirationDate; }

    public AccountingLinkExpirationReason getExpirationReason() { return expirationReason; }

    public void setExpirationReason(AccountingLinkExpirationReason expirationReason) { this.expirationReason = expirationReason; }

    public Long getRelatedEntityId() { return relatedEntityId; }

    public void setRelatedEntityId(Long relatedEntityId) { this.relatedEntityId = relatedEntityId; }

    public String getRelatedEntityType() { return relatedEntityType; }

    public void setRelatedEntityType(String relatedEntityType) { this.relatedEntityType = relatedEntityType; }

    public Long getRelatedFlowId() { return relatedFlowId; }

    public void setRelatedFlowId(Long relatedFlowId) { this.relatedFlowId = relatedFlowId; }

    public String getRelatedFlowEntityType() { return relatedFlowEntityType; }

    public void setRelatedFlowEntityType(String relatedFlowEntityType) { this.relatedFlowEntityType = relatedFlowEntityType; }

    @JsonIgnore
    public Map<String, Object> getAdditionalInfo() {

        if (additionalInfo != null) {
            try {
                return Setup.getApplicationContext().getBean(ObjectMapper.class)
                        .readValue(additionalInfo, new TypeReference<Map<String, Object>>() {
                        });
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
        }
        return new HashMap<>();
    }

    public void setAdditionalInfo(Map<String, Object> additionalInfo) {
        try {
            this.additionalInfo =
                    Setup.getApplicationContext().getBean(ObjectMapper.class)
                            .writeValueAsString(additionalInfo);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
    }

    @JsonIgnore
    public Object getAdditionalValue(String key) {
        return getAdditionalInfo().get(key);
    }

    public void setAdditionalValue(String key, Object value) {

        Map<String, Object> additionalInfo = getAdditionalInfo();
        additionalInfo.put(key, value);
        setAdditionalInfo(additionalInfo);
    }

    public static class AccountingLinkBuilder {
        private Long contractId;
        private Long cptId;

        private String originalLink;
        private String shortenedLink;
        private AccountingLinkType type;
        private AccountingLinkStatus status = AccountingLinkStatus.PENDING;
        private Date doneDate;
        private Date expirationDate;
        private AccountingLinkExpirationReason expirationReason;

        private Long relatedEntityId;
        private String relatedEntityType;
        private Long relatedFlowId;
        private String relatedFlowEntityType;

        private boolean ignoreDuplication = false;
        private Map<String, Object> additionalInfo;

        public AccountingLinkBuilder() { }

        public AccountingLinkBuilder AccountingLink(
                Long relatedEntityId, String relatedEntityType,
                AccountingLinkType type, String originalLink) {
            this.relatedEntityId = relatedEntityId;
            this.relatedEntityType = relatedEntityType;
            this.type = type;
            this.originalLink = originalLink;
            return this;
        }

        public AccountingLinkBuilder setContractId(Long contractId) {
            this.contractId = contractId;
            return this;
        }

        public AccountingLinkBuilder setCptId(Long cptId) {
            this.cptId = cptId;
            return this;
        }

        public AccountingLinkBuilder setOriginalLink(String originalLink) {
            this.originalLink = originalLink;
            return this;
        }

        public AccountingLinkBuilder setShortenedLink(String shortenedLink) {
            this.shortenedLink = shortenedLink;
            return this;
        }

        public AccountingLinkBuilder setType(AccountingLinkType type) {
            this.type = type;
            return this;
        }

        public AccountingLinkBuilder setStatus(AccountingLinkStatus status) {
            this.status = status;
            return this;
        }

        public AccountingLinkBuilder setDoneDate(Date doneDate) {
            this.doneDate = doneDate;
            return this;
        }

        public AccountingLinkBuilder setExpirationDate(Date expirationDate) {
            this.expirationDate = expirationDate;
            return this;
        }

        public AccountingLinkBuilder setExpirationReason(AccountingLinkExpirationReason expirationReason) {
            this.expirationReason = expirationReason;
            return this;
        }

        public AccountingLinkBuilder setRelatedEntityId(Long relatedEntityId) {
            this.relatedEntityId = relatedEntityId;
            return this;
        }

        public AccountingLinkBuilder setRelatedEntityType(String relatedEntityType) {
            this.relatedEntityType = relatedEntityType;
            return this;
        }

        public AccountingLinkBuilder setRelatedFlowId(Long relatedFlowId) {
            this.relatedFlowId = relatedFlowId;
            return this;
        }

        public AccountingLinkBuilder setRelatedFlowEntityType(String relatedFlowEntityType) {
            this.relatedFlowEntityType = relatedFlowEntityType;
            return this;
        }

        public AccountingLinkBuilder setIgnoreDuplication(boolean ignoreDuplication) {
            this.ignoreDuplication = ignoreDuplication;
            return this;
        }

        public AccountingLinkBuilder setAdditionalInfo(Map<String, Object> additionalInfo) {
            this.additionalInfo = additionalInfo;
            return this;
        }

        public AccountingLink build() {
            AccountingLinkRepository accountingLinkRepository = Setup.getRepository(AccountingLinkRepository.class);
            Shortener shortener = Setup.getApplicationContext()
                    .getBean(Shortener.class);
            AccountingLink a;
            if (!ignoreDuplication) {
                a = accountingLinkRepository.findFirstByRelatedEntityIdAndRelatedEntityTypeAndTypeAndStatusAndOriginalLink(
                        this.relatedEntityId, this.relatedEntityType, this.type, this.status, this.originalLink);

                if (a != null) {
                    return a;
                }
            }

            a = new AccountingLink();
            a.setContractId(this.contractId);
            a.setCptId(this.cptId);
            a.setOriginalLink(this.originalLink);
            a.setShortenedLink(this.shortenedLink);
            a.setType(this.type);
            a.setDoneDate(this.doneDate);
            a.setExpirationDate(this.expirationDate);
            a.setStatus(this.status);
            a.setExpirationReason(this.expirationReason);
            a.setRelatedEntityId(this.relatedEntityId);
            a.setRelatedEntityType(this.relatedEntityType);
            a.setRelatedFlowId(this.relatedFlowId);
            a.setRelatedFlowEntityType(this.relatedFlowEntityType);
            if (this.additionalInfo != null) {
                a.setAdditionalInfo(this.additionalInfo);
            }

            if (this.shortenedLink == null) {
                a.setShortenedLink(shortener.shorten(a.getOriginalLink()));
            }

            a = accountingLinkRepository.save(a);
            return a;
        }
    }
}