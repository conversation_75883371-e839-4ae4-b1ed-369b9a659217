package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.serializer.BucketIdLabelCodeSerializer;
import com.magnamedia.module.type.VatType;

import javax.persistence.*;
import java.sql.Date;

/**
 * <AUTHOR> masod <<EMAIL>>
 *         Created on Sep 22, 2020
 *         ACC-2570
 */

@Entity
public class WireTransferTempTransaction extends BaseEntity {

    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private WireTransferTempPayment wireTransferPayment;

    @Column
    private Long generatedTransactionId;

    @Column(columnDefinition = "double default 0")
    private Double amount = 0.0;

    @Column
    private Date transactionDate;

    @Column
    private Date pnlValueDate;

    @Column(columnDefinition = "double default 0")
    private Double vatAmount = 0D;

    @Column
    @Enumerated(EnumType.STRING)
    private VatType vatType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private Revenue revenue;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = BucketIdLabelCodeSerializer.class)
    private Bucket toBucket;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem license;

    @Lob
    private String description;

    @Column(columnDefinition = "boolean default false")
    private boolean confirmed = false;

    public WireTransferTempPayment getWireTransferPayment() {
        return wireTransferPayment;
    }

    public void setWireTransferPayment(WireTransferTempPayment wireTransferPayment) {
        this.wireTransferPayment = wireTransferPayment;
    }

    public Long getGeneratedTransactionId() {
        return generatedTransactionId;
    }

    public void setGeneratedTransactionId(Long generatedTransactionId) {
        this.generatedTransactionId = generatedTransactionId;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Date getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(Date transactionDate) {
        this.transactionDate = transactionDate;
    }

    public Date getPnlValueDate() {
        return pnlValueDate;
    }

    public void setPnlValueDate(Date pnlValueDate) {
        this.pnlValueDate = pnlValueDate;
    }

    public Double getVatAmount() {
        return vatAmount;
    }

    public void setVatAmount(Double vatAmount) {
        this.vatAmount = vatAmount;
    }

    public VatType getVatType() {
        return vatType;
    }

    public void setVatType(VatType vatType) {
        this.vatType = vatType;
    }

    public Revenue getRevenue() {
        return revenue;
    }

    public void setRevenue(Revenue revenue) {
        this.revenue = revenue;
    }

    public Bucket getToBucket() {
        return toBucket;
    }

    public void setToBucket(Bucket toBucket) {
        this.toBucket = toBucket;
    }

    public PicklistItem getLicense() {
        return license;
    }

    public void setLicense(PicklistItem license) {
        this.license = license;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public boolean isConfirmed() {
        return confirmed;
    }

    public void setConfirmed(boolean confirmed) {
        this.confirmed = confirmed;
    }
}
