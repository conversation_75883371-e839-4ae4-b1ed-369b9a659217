package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdLabelCodeSerializer;
import com.magnamedia.module.AccountingModule;
import org.hibernate.annotations.ColumnDefault;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

/**
 * Created by Mamon.Masod on 5/2/2021.
 */

@Entity
public class DirectDebitConfiguration extends BaseEntity {

    public static DirectDebitConfiguration newInstance(PicklistItem bank) {
        DirectDebitConfiguration directDebitConfiguration = new DirectDebitConfiguration();
        directDebitConfiguration.setBank(bank);
        directDebitConfiguration.setNumberOfGeneratedDDs(Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_CONFIGURATION_DEFAULT_NBR_OF_GENERATED_DDS)));
        directDebitConfiguration.setDdaTimeFrame(Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_CONFIGURATION_DEFAULT_DDA_TIME_FRAME)));
        directDebitConfiguration.setCreateManualForDDB(Boolean.parseBoolean(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_CONFIGURATION_DEFAULT_CREATE_MANUAL_FOR_DDB)));
        directDebitConfiguration.setIncludeManualInDDBFlow(Boolean.parseBoolean(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_CONFIGURATION_DEFAULT_INCLUDE_MANUAL_IN_DDB_FLOW)));

        return directDebitConfiguration;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelCodeSerializer.class)
    private PicklistItem bank;

    @Column
    @ColumnDefault("3")
    private Integer numberOfGeneratedDDs = 3;

    @Column
    @ColumnDefault("3")
    private Integer ddaTimeFrame = 3;

    @Column
    @ColumnDefault("1")
    private boolean createManualForDDB = Boolean.TRUE;

    @Column
    @ColumnDefault("1")
    private boolean includeManualInDDBFlow = Boolean.TRUE;

    public PicklistItem getBank() {
        return bank;
    }

    public void setBank(PicklistItem bank) {
        this.bank = bank;
    }

    public Integer getNumberOfGeneratedDDs() {
        return numberOfGeneratedDDs;
    }

    public void setNumberOfGeneratedDDs(Integer numberOfGeneratedDDs) {
        this.numberOfGeneratedDDs = numberOfGeneratedDDs;
    }

    public Integer getDdaTimeFrame() {
        return ddaTimeFrame;
    }

    public void setDdaTimeFrame(Integer ddaTimeFrame) {
        this.ddaTimeFrame = ddaTimeFrame;
    }

    public boolean isCreateManualForDDB() {
        return createManualForDDB;
    }

    public void setCreateManualForDDB(boolean createManualForDDB) {
        this.createManualForDDB = createManualForDDB;
    }

    public boolean isIncludeManualInDDBFlow() {
        return includeManualInDDBFlow;
    }

    public void setIncludeManualInDDBFlow(boolean includeManualInDDBFlow) {
        this.includeManualInDDBFlow = includeManualInDDBFlow;
    }
}
