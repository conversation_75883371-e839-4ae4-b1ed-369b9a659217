package com.magnamedia.entity;

import com.magnamedia.core.entity.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Entity;

/**
 * <PERSON> (Jan 30, 2021)
 */
@Entity
public class UnitOfMeasure extends BaseEntity {
    @Column(unique = true)
    private String unitOfMeasureId;
    private String fullName;
    private String shortName;

    public String getUnitOfMeasureId() {
        return unitOfMeasureId;
    }

    public void setUnitOfMeasureId(String unitOfMeasureId) {
        this.unitOfMeasureId = unitOfMeasureId;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getShortName() {
        return shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName;
    }
}
