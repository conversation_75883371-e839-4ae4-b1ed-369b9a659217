package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.entity.serializer.PaymentJsonSerializer;
import com.magnamedia.module.type.PaymentMatchingRecordStatus;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.PaymentRepository;

import java.util.Arrays;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.persistence.*;

import org.hibernate.envers.NotAudited;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Mar 12, 2019
 *         Jirra ACC-469
 */
@Entity
public class PaymentMatchingRecord extends BaseEntity {

    @Transient
    public final static List<PaymentStatus> notMatchingStatuses =
            Arrays.asList(new PaymentStatus[]{
                    PaymentStatus.DELETED, PaymentStatus.CANCELLED,
                    PaymentStatus.RECEIVED, PaymentStatus.CANCELLED_WAITING_CLIENT_PICKUP});

    @Column
    private String paymentIdStr;

    @Column
    private String status;

    //Jirra ACC-943
    @Column
    private String reasonOfBouncedCheque;

    @Column
    private PaymentStatus prevStatus;

    // Jirra ACC-599
    @Column
    private Boolean confirmed = false;

    @JsonIgnore
    @NotAudited
    @ManyToOne
    private PaymentsMatchingFile paymentsMatchingFile;

    @NotAudited
    @ManyToOne
    @JsonSerialize(using = PaymentJsonSerializer.class)
    private Payment payment;

    //Jirra ACC-1870
    @Enumerated(EnumType.STRING)
    private PaymentMatchingRecordStatus recordStatus = PaymentMatchingRecordStatus.NOT_MATCHED;

    //Jirra ACC-1870
    @Column
    private String transferringFailureReason;

    public String getPaymentIdStr() {
        return paymentIdStr;
    }

    public void setPaymentIdStr(String paymentIdStr) {
        this.paymentIdStr = paymentIdStr;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReasonOfBouncedCheque() {
        return reasonOfBouncedCheque;
    }

    public void setReasonOfBouncedCheque(String reasonOfBouncedCheque) {
        this.reasonOfBouncedCheque = reasonOfBouncedCheque;
    }

    public PaymentStatus getPrevStatus() {
        return prevStatus;
    }

    public void setPrevStatus(PaymentStatus prevStatus) {
        this.prevStatus = prevStatus;
    }

    public Boolean getConfirmed() {
        return confirmed;
    }

    public void setConfirmed(Boolean confirmed) {
        this.confirmed = confirmed;
    }

    public PaymentsMatchingFile getPaymentsMatchingFile() {
        return paymentsMatchingFile;
    }

    public void setPaymentsMatchingFile(PaymentsMatchingFile paymentsMatchingFile) {
        this.paymentsMatchingFile = paymentsMatchingFile;
    }

    public Payment getPayment() {
        return payment;
    }

    public void setPayment(Payment payment) {
        this.payment = payment;
    }

    public PaymentMatchingRecordStatus getRecordStatus() {
        return recordStatus;
    }

    public void setRecordStatus(PaymentMatchingRecordStatus recordStatus) {
        this.recordStatus = recordStatus;
    }

    public String getTransferringFailureReason() {
        return transferringFailureReason;
    }

    public void setTransferringFailureReason(String transferringFailureReason) {
        this.transferringFailureReason = transferringFailureReason;
    }

    public void validate() {
        if (this.paymentsMatchingFile == null)
            throw new RuntimeException("File is mandatory.");
    }

    @BeforeInsert
    public void validateInsert() {
        PaymentRepository paymentRepository =
                Setup.getApplicationContext().getBean(PaymentRepository.class);
        validate();
        try {
            Payment p = paymentRepository.findOne(Long.parseLong(paymentIdStr));
            if (p != null
                    && !notMatchingStatuses.contains(p.getStatus())) {
                this.payment = p;
                this.prevStatus = p.getStatus();
                this.recordStatus = PaymentMatchingRecordStatus.MATCHED;
            }
        } catch (Exception e) {
            Logger.getLogger(BankDirectDebitActivationFile.class.getName()).log(Level.SEVERE, null, e);
        }
    }

}
