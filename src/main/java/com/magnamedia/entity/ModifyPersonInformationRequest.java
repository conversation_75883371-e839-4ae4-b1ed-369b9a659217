package com.magnamedia.entity;

import com.magnamedia.core.workflow.FormField;

import javax.persistence.Entity;
import java.io.Serializable;
import java.util.List;

@Entity
public class ModifyPersonInformationRequest extends VisaRequest<ModifyPersonInformationRequest, ModifyPersonInformationNote, ModifyPersonInformationExpense> implements Serializable {

    public ModifyPersonInformationRequest() {
        super(null);
    }

    public ModifyPersonInformationRequest(String startTaskName) {
        super(startTaskName);
    }

    @Override
    public String getFinishedTaskName() {
        return null;
    }

    @Override
    public List<FormField> getForm(String taskName) {
        return null;
    }
}
