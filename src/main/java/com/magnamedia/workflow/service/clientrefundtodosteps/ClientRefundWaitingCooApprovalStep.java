package com.magnamedia.workflow.service.clientrefundtodosteps;


import com.magnamedia.controller.PayrollAccountantTodoController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.entity.workflow.PayrollAccountantTodo;
import com.magnamedia.helper.UserHelper;
import com.magnamedia.service.ClientRefundService;
import com.magnamedia.workflow.service.ClientRefundManualStep;
import com.magnamedia.workflow.type.ClientRefundPaymentMethod;
import com.magnamedia.workflow.type.ClientRefundStatus;
import com.magnamedia.workflow.type.ClientRefundTodoType;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 * Created on Dec 12, 2020
 * ACC-2847
 */

@Service
public class ClientRefundWaitingCooApprovalStep
        extends ClientRefundManualStep<ClientRefundToDo> {

    private static final Logger logger =
            Logger.getLogger(ClientRefundWaitingCooApprovalStep.class.getName());


    public ClientRefundWaitingCooApprovalStep() {
        this.setId(ClientRefundTodoType.WAITING_COO_APPROVAL.toString());
    }

    @Override
    public void onSave(ClientRefundToDo entity) {
    }

    @Override
    public void postSave(ClientRefundToDo entity) {
    }

    @Override
    public void onDone(ClientRefundToDo entity) {
        logger.log(Level.INFO, "Client Refund Coo Approval Step Started");

        if (entity.getCeoAction() == null)
            throw new RuntimeException("COO Action can't be null");

        if (!entity.getStatus().equals(ClientRefundStatus.PENDING))
            throw new RuntimeException("Status is not pending you can't do this action");

        if (!UserHelper.hasFamilyRefundCooPosition())
            throw new RuntimeException("User can't do this action");

        switch(entity.getCeoAction()) {
            case APPROVE:
                if (entity.getMethodOfPayment().equals(ClientRefundPaymentMethod.BANK_TRANSFER)) {
                    addNewTask(entity, ClientRefundTodoType.BANK_TRANSFER_CREATED.toString());

                    PayrollAccountantTodoController payrollAccountantTodoController = Setup.getApplicationContext().getBean(PayrollAccountantTodoController.class);
                    PayrollAccountantTodo payrollAccountantTodo = new PayrollAccountantTodo();
                    payrollAccountantTodo.setClientRefundToDo(entity);
                    payrollAccountantTodoController.createEntity(payrollAccountantTodo);
                } else if (entity.getMethodOfPayment().equals(ClientRefundPaymentMethod.CREDIT_CARD)) {
                    addNewTask(entity, ClientRefundTodoType.CREDIT_CARD_TRANSFER_CREATED.toString());

                    if (!Setup.getApplicationContext()
                            .getBean(ClientRefundService.class)
                            .approveCreditCardRefund(entity)) return;
                }
                break;
            case REJECT:
                entity.setStopped(true);
                entity.setCompleted(true);
                entity.setStatus(ClientRefundStatus.REJECTED);
                entity.setStatusChangeDate(new java.sql.Date(new java.util.Date().getTime()));
                break;
            default:
                throw new RuntimeException("COO Action has unexpected value");
        }

        entity.setCeoActionBy(CurrentRequest.getUser());
        super.onDone(entity);
    }

    @Override
    public void postDone(ClientRefundToDo entity) {
    }

    public List<String> getTaskHeader() {
        return Arrays.asList("client.label", "purpose.label", "amount", "attachments", "notes", "detail");
    }
}
