package com.magnamedia.workflow.service.clientrefundtodosteps;


import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.repository.ClientRefundTodoRepository;
import com.magnamedia.workflow.service.ClientRefundManualStep;
import com.magnamedia.workflow.type.ClientRefundTodoType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 * Created on Dec 12, 2020
 * ACC-2847
 */

@Service
public class ClientRefundBankTransferCreatedStep
        extends ClientRefundManualStep<ClientRefundToDo> {

    @Autowired
    private ClientRefundTodoRepository clientRefundTodoRepository;

    private static final Logger logger =
            Logger.getLogger(ClientRefundBankTransferCreatedStep.class.getName());


    public ClientRefundBankTransferCreatedStep() {
        this.setId(ClientRefundTodoType.BANK_TRANSFER_CREATED.toString());
    }

    @Override
    public void onSave(ClientRefundToDo entity) {
    }

    @Override
    public void postSave(ClientRefundToDo entity) {
    }

    @Override
    public void onDone(ClientRefundToDo entity) {

        logger.log(Level.INFO, "Client Refund Bank Transfer Created Step Started");

    }

    @Override
    public void postDone(ClientRefundToDo entity) {
    }
}
