package com.magnamedia.workflow.service.clientrefundtodosteps;

import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.repository.ClientRefundTodoRepository;
import com.magnamedia.workflow.service.ClientRefundManualStep;
import com.magnamedia.workflow.type.ClientRefundTodoType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.logging.Level;
import java.util.logging.Logger;

/*

 * <AUTHOR>
 * @created 23/03/2024 - 5:02 PM
 * ACC-6805

 */
@Service
public class ClientRefundCreditCardTransferCreatedStep extends ClientRefundManualStep<ClientRefundToDo> {

    @Autowired
    private ClientRefundTodoRepository clientRefundTodoRepository;

    private static final Logger logger =
            Logger.getLogger(ClientRefundCreditCardTransferCreatedStep.class.getName());


    public ClientRefundCreditCardTransferCreatedStep() {
        this.setId(ClientRefundTodoType.CREDIT_CARD_TRANSFER_CREATED.toString());
    }

    @Override
    public void onSave(ClientRefundToDo entity) {}

    @Override
    public void postSave(ClientRefundToDo entity) {}

    @Override
    public void onDone(ClientRefundToDo entity) {
        logger.log(Level.INFO, "Client Refund Credit Card Transfer Created Step Started");
    }

    @Override
    public void postDone(ClientRefundToDo entity) {}
}