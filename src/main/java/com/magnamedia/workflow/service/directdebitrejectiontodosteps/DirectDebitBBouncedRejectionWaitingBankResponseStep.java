package com.magnamedia.workflow.service.directdebitrejectiontodosteps;

import com.magnamedia.businessrule.DirectDebitFileBusinessRule;
import com.magnamedia.controller.DirectDebitController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.DirectDebitMethod;
import com.magnamedia.module.type.DirectDebitRejectionToDoType;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.DirectDebitRejectionToDoRepository;
import com.magnamedia.service.BouncingFlowService;
import com.magnamedia.service.ContractService;
import com.magnamedia.service.DirectDebitRejectionFlowService;
import com.magnamedia.service.DirectDebitSignatureService;
import com.magnamedia.workflow.service.DirectDebitRejectionToDoManualStep;
import org.joda.time.LocalDateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on 5-3-2020
 *         Jirra ACC-1777
 */
@Service
public class DirectDebitBBouncedRejectionWaitingBankResponseStep
        extends DirectDebitRejectionToDoManualStep<DirectDebitRejectionToDo> {

    @Autowired
    private DirectDebitController directDebitCtrl;
    @Autowired
    private BouncingFlowService bouncingFlowService;
    @Autowired
    private DirectDebitRejectionFlowService directDebitRejectionFlowService;
    @Autowired
    private DirectDebitRejectionToDoRepository directDebitRejectionToDoRepository;

    @Autowired
    private  DirectDebitSignatureService directDebitSignatureService;

    private static final Logger logger =
            Logger.getLogger(DirectDebitFileBusinessRule.class.getName());

    public DirectDebitBBouncedRejectionWaitingBankResponseStep() {
        this.setId(DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE_B_BOUNCED.toString());
    }

    @Override
    public void onSave(DirectDebitRejectionToDo entity) {
    }

    @Override
    public void postSave(DirectDebitRejectionToDo entity) {
    }

    @Override
    public void onDone(DirectDebitRejectionToDo entity) {
        super.onDone(entity);

        List<DirectDebit> directDebitsList = entity.getDirectDebitsForBouncingFlow();
        for (DirectDebit directDebit : directDebitsList) {
            SelectQuery<Payment> query = new SelectQuery<>(Payment.class);
            query.filterBy("directDebitId", "=", directDebit.getId());
            query.filterBy("replaced", "=", false);
            query.filterBy("status", "=", PaymentStatus.BOUNCED);
            query.filterBy("bouncedFlowPausedForReplacement", "=", true);

            List<Payment> payments = query.execute();

            if(!payments.isEmpty()) {
                logger.log(Level.SEVERE, "DirectDebitBBouncedRejectionWaitingBankResponseStep directDebitRejectionToDo id:" + entity.getId());
                logger.log(Level.SEVERE, "DirectDebitBBouncedRejectionWaitingBankResponseStep directDebitRejectionToDo is on direct debit that has paused payment for bouncing flow");
                logger.log(Level.SEVERE, "DirectDebitBBouncedRejectionWaitingBankResponseStep directDebit id:" + directDebit.getId());
                logger.log(Level.SEVERE, "DirectDebitBBouncedRejectionWaitingBankResponseStep paused payment id:" + payments.get(0).getId());

                addNewTask(entity, DirectDebitRejectionToDoType.WAITING_FLOW_PAUSE_B_BOUNCED.toString());
                return;
            }
        }

        DirectDebit lastRejected = entity.getLastDirectDebit();

        if (lastRejected != null) {
            ContractPaymentTerm contractPaymentTerm = lastRejected.getContractPaymentTerm();

            logger.log(Level.SEVERE, "DirectDebitBBouncedRejectionWaitingBankResponseStep onDone dd id:" + lastRejected.getId());
            logger.log(Level.SEVERE, "DirectDebitBBouncedRejectionWaitingBankResponseStep onDone dd getBouncingRejectCategory:" + lastRejected.getBouncingRejectCategory());

            boolean allManualRejected = directDebitRejectionFlowService.allManualFilesRejected(
                    lastRejected.getDirectDebitFiles());

            logger.log(Level.SEVERE, "DirectDebitBBouncedRejectionWaitingBankResponseStep onDone dd allManualRejected:" + allManualRejected);

            if (allManualRejected) {
                Integer maxTrials =
                        Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                                AccountingModule.PARAMETER_DD_MAX_TRIALS));

                /*boolean createExpertTodo = false;
                String reasonToCall = "";
                String initialNotes = "";*/
                boolean scheduleForTermination = false;
                boolean dontSendDdMessage = false;
                boolean leadingRejectionFlow = false;

                if (lastRejected.getBouncingRejectCategory() != entity.getLastRejectCategory()) {
                    logger.log(Level.SEVERE, "DirectDebitBBouncedRejectionWaitingBankResponseStep make trials 0 because of change rejection category" +
                            " to : " + lastRejected.getRejectCategory() + " from : " + entity.getLastRejectCategory());
                    entity.setTrials(0);
                }

                if (null != lastRejected.getBouncingRejectCategory()) switch (lastRejected.getBouncingRejectCategory()) {
                    case Compliance:
                    case Other: {
                        Map<String, Object> signatureType = directDebitSignatureService
                                .getLastSignatureType(lastRejected.getContractPaymentTerm(), true, false);
                        List<DirectDebitSignature> signatures = (List<DirectDebitSignature>) signatureType.get("currentSignatures");
                        try {
                            Map<String, Object> map = new HashMap<>();
                            map.put("signatures", signatures);
                            bouncingFlowService.createManualDDsForBouncedFlow(lastRejected, map);
//                        bouncingFlowService.increaseDDPaymentsTrials(lastRejected);
                        } catch (Exception e) {
                            e.printStackTrace();
                            throw new RuntimeException(e.getMessage());
                        }
                        
                        addNewTask(entity, DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE_B_BOUNCED.toString());
                        break;
                    }
                    case Signature:{
                        Integer maxReSignTrials =
                                Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                                        AccountingModule.PARAMETER_DD_MAX_RE_SIGN_TRIALS));
                        logger.log(Level.SEVERE, "DirectDebitARejectionWaitingBankResponseStep maxReSignTrials: " + maxReSignTrials);
                        logger.log(Level.SEVERE, "DirectDebitARejectionWaitingBankResponseStep entity.getReSignTrials(): " + entity.getReSignTrials());

                        //ACC-4715
                        if (directDebitRejectionFlowService.flowStoppedAfterIncreaseReSignTrials(entity)) return;

                        entity.setReSignTrials(entity.getReSignTrials() + 1);
                        
                        if (entity.getReSignTrials() > maxReSignTrials) {
                            scheduleForTermination = true;    
                        } else if (entity.getReSignTrials() == maxReSignTrials) {
                            bouncingFlowService.createManualDDsForBouncedFlow(lastRejected, new HashMap<>());
//                        bouncingFlowService.increaseDDPaymentsTrials(lastRejected);

                            entity.setReminder(0);
                            entity.setReminderDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());

                            // ACC-2860
                            leadingRejectionFlow = !directDebitRejectionFlowService.existOtherWaitingClientSignatureFlow(
                                    contractPaymentTerm.getContract(), Arrays.asList(lastRejected.getId()));

                            /*createExpertTodo = leadingRejectionFlow;
                            reasonToCall = "Client signature is rejected from bank side for " + maxReSignTrials + " time";*/

                            // make the rejection flow waits for client resign
                            addNewTask(entity, DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_BOUNCED.toString());

                        } else if (entity.getReSignTrials() < maxReSignTrials) {
                            Map<String, Object> map = new HashMap<>();
                            if (directDebitRejectionFlowService.shouldGenerateNewDdUsingOldSignatures(
                                    entity.getReSignTrials(), contractPaymentTerm, map)) {

                                try {
                                    bouncingFlowService.createManualDDsForBouncedFlow(lastRejected, map);
//                                bouncingFlowService.increaseDDPaymentsTrials(lastRejected);
                                    directDebitRejectionFlowService.mergePendingDataEntryDDsIntoOneToDo(lastRejected);
                                } catch (Exception e) {
                                    e.printStackTrace();
                                    throw new RuntimeException(e.getMessage());
                                }
                                
                                dontSendDdMessage = true;
                                addNewTask(entity, DirectDebitRejectionToDoType.WAITING_BANK_RESPONSE_B_BOUNCED.toString());
                            } else { // not using same signature
                                bouncingFlowService.createManualDDsForBouncedFlow(lastRejected, new HashMap<>());
//                            bouncingFlowService.increaseDDPaymentsTrials(lastRejected);

                                entity.setReminder(0);
                                entity.setReminderDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());

                                //Jirra ACC-2860
                                leadingRejectionFlow = !directDebitRejectionFlowService.existOtherWaitingClientSignatureFlow(
                                        contractPaymentTerm.getContract(), Arrays.asList(lastRejected.getId()));

                                addNewTask(entity, DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_BOUNCED.toString());
                            }
                        }
                        break;
                    }
                    
                    /*case Account:
                    case EID:
                        lastRejected.setMStatus(DirectDebitStatus.PENDING_DATA_ENTRY);
                        lastRejected.setManualDdfFile(null);
                        lastRejected.setConfirmedBankInfo(false);
                        List<DirectDebitFile> manualsForBouncing = lastRejected.getDirectDebitFiles()
                                .stream()
                                .filter(ddf -> ddf.getDdMethod() == DirectDebitMethod.MANUAL && ddf.getDdStatus().equals(DirectDebitStatus.REJECTED)
                                        && ddf.getForBouncingPayment() != null && ddf.getForBouncingPayment())
                                .collect(Collectors.toList());
                        directDebitRejectionFlowService.sendDDFsBackToAccountant(manualsForBouncing);
                        dontSendDdMessage = true;
                        
                        addNewTask(entity, DirectDebitRejectionToDoType.WAITING_ACCOUNTANT_ACTION_B_BOUNCED.toString());
                        break;*/

                    case Account:
                    case EID:
                    case Invalid_Account:
                    case Authorization:{
                        bouncingFlowService.createManualDDsForBouncedFlow(lastRejected, new HashMap<>());
//                    bouncingFlowService.increaseDDPaymentsTrials(lastRejected);
                        entity.setTrials(entity.getTrials() + 1);
                        entity.setReminder(0);
                        entity.setReminderDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());
                        
                        if (entity.getTrials() > maxTrials) {
                            scheduleForTermination = true;
                        }
                        
                        /*if (entity.getTrials() == (maxTrials - 1)) {
                            createExpertTodo = true;
                            reasonToCall = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_AUTH_REJECT_EXPERT_TODO_REASON_TO_CALL);
                            initialNotes = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_DD_AUTH_REJECT_EXPERT_TODO_INITIAL_NOTES);
                        }   */
                        
                        // make the rejection flow waits for client to provide new info
                        addNewTask(entity, DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_BOUNCED.toString());
                        leadingRejectionFlow = !directDebitRejectionFlowService.existOtherWaitingClientSignatureFlow(
                                contractPaymentTerm.getContract(), Arrays.asList(lastRejected.getId()));
                        break;
                    }
                    
                    /*case Invalid_Account:{
                        bouncingFlowService.createManualDDsForBouncedFlow(lastRejected, new HashMap<>());
//                    bouncingFlowService.increaseDDPaymentsTrials(lastRejected);
                        entity.setTrials(entity.getTrials() + 1);
                        entity.setReminder(0);
                        entity.setReminderDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());
                        if (entity.getTrials() > maxTrials) {
                            scheduleForTermination = true;
                        }
                        
                        // make the rejection flow waits for client resign
                        addNewTask(entity, DirectDebitRejectionToDoType.WAITING_CLIENT_SIGNATURE_B_BOUNCED.toString());
                        leadingRejectionFlow = !directDebitRejectionFlowService.existOtherWaitingClientSignatureFlow(
                                contractPaymentTerm.getContract(), Arrays.asList(lastRejected.getId()));
                        break;
                    }*/
                    default:
                        break;
                }

                entity.setDontSendDdMessage(dontSendDdMessage);
                entity.setLeadingRejectionFlow(leadingRejectionFlow);

                logger.log(Level.SEVERE, "DirectDebitBBouncedRejectionWaitingBankResponseStep onDone entity id:" + entity.getId());
                logger.log(Level.SEVERE, "DirectDebitBBouncedRejectionWaitingBankResponseStep onDone setDontSendDdMessage:" + entity.getDontSendDdMessage());

                if (scheduleForTermination) {
                    Contract contract = lastRejected.getContractPaymentTerm().getContract();
                    
                    if (contract.isTerminateContractDueRejection() || !entity.isDdAddedByOecFlow()) {
                        logger.info("contract.terminateContractDueRejection: " + contract.isTerminateContractDueRejection() +
                                "; entity.isDdAddedByOecFlow: " + entity.isDdAddedByOecFlow());

                        entity.setContractScheduleDateOfTermination(
                                directDebitRejectionFlowService
                                        .setContractForTermination(
                                                lastRejected.getContractPaymentTerm(),
                                                "direct_debit_rejection_type_b_maxsignaturetrialsb_reached",
                                                entity));
                        entity.setLeadingRejectionFlow(true);
                    }
                    entity.setCompleted(true);
                    entity.setStopped(true);
                }

                /*if (createExpertTodo) {
                    entity.setVoiceResolverTodoId(directDebitRejectionFlowService.createExpertTodo(lastRejected, reasonToCall, initialNotes));
                    entity.setLastTrialDate(new LocalDateTime().withMinuteOfHour(0).withSecondOfMinute(0).toDate());
                }*/

                entity.setLastRejectCategory(lastRejected.getBouncingRejectCategory());
                directDebitRejectionToDoRepository.save(entity);
            }
        }
    }

    @Override
    public void postDone(DirectDebitRejectionToDo entity) {
    }
}
