package com.magnamedia.module.type;


import com.magnamedia.extra.LabelValueEnum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on Feb 29, 2020
 * Jirra ACC-1435
 */

public enum DDMsgConfigType implements LabelValueEnum {

    DD_PENDING_INFO("DD Pending Info"),
    DD_PENDING_INFO_REMINDER_1("DD Pending Info Reminder 1"),
    DD_PENDING_INFO_REMINDER_2("DD Pending Info Reminder 2"),
    DD_PENDING_INFO_REMINDER_3("DD Pending Info Reminder 3"),
    DD_PENDING_INFO_REMINDER_4("DD Pending Info Reminder 4"),
    DD_PENDING_INFO_REMINDER_5("DD Pending Info Reminder 5"),

    // jira acc 1542
    DD_REJECTED_INFO_ALL("DD Rejected Info All"),
    DD_REJECTED_INFO_ALL_REMINDER_1("DD Rejected Info All Reminder 1"),
    DD_REJECTED_INFO_ALL_REMINDER_2("DD Rejected Info All Reminder 2"),
    DD_REJECTED_INFO_ALL_REMINDER_3("DD Rejected Info All Reminder 3"),
    DD_REJECTED_INFO_ALL_REMINDER_4("DD Rejected Info All Reminder 4"),
    DD_REJECTED_INFO_ALL_REMINDER_5("DD Rejected Info All Reminder 5"),


    DD_REJECTED_INFO_EID_IBAN("DD Rejected Info EID And IBAN"),
    DD_REJECTED_INFO_EID_IBAN_REMINDER_1("DD Rejected Info EID And IBAN Reminder 1"),
    DD_REJECTED_INFO_EID_IBAN_REMINDER_2("DD Rejected Info EID And IBAN Reminder 2"),
    DD_REJECTED_INFO_EID_IBAN_REMINDER_3("DD Rejected Info EID And IBAN Reminder 3"),
    DD_REJECTED_INFO_EID_IBAN_REMINDER_4("DD Rejected Info EID And IBAN Reminder 4"),
    DD_REJECTED_INFO_EID_IBAN_REMINDER_5("DD Rejected Info EID And IBAN Reminder 5"),

    DD_REJECTED_INFO_EID_ACCOUNT_NAME_HOLDER("DD Rejected Info EID And Account Name Holder"),
    DD_REJECTED_INFO_EID_ACCOUNT_NAME_HOLDER_REMINDER_1("DD Rejected Info EID And Account Name Holder Reminder 1"),
    DD_REJECTED_INFO_EID_ACCOUNT_NAME_HOLDER_REMINDER_2("DD Rejected Info EID And Account Name Holder Reminder 2"),
    DD_REJECTED_INFO_EID_ACCOUNT_NAME_HOLDER_REMINDER_3("DD Rejected Info EID And Account Name Holder Reminder 3"),
    DD_REJECTED_INFO_EID_ACCOUNT_NAME_HOLDER_REMINDER_4("DD Rejected Info EID And Account Name Holder Reminder 4"),
    DD_REJECTED_INFO_EID_ACCOUNT_NAME_HOLDER_REMINDER_5("DD Rejected Info EID And Account Name Holder Reminder 5"),


    DD_REJECTED_INFO_IBAN_ACCOUNT_NAME_HOLDER("DD Rejected Info IBAN And Account Name Holder"),
    DD_REJECTED_INFO_IBAN_ACCOUNT_NAME_HOLDER_REMINDER_1("DD Rejected Info IBAN And Account Name Holder Reminder 1"),
    DD_REJECTED_INFO_IBAN_ACCOUNT_NAME_HOLDER_REMINDER_2("DD Rejected Info IBAN And Account Name Holder Reminder 2"),
    DD_REJECTED_INFO_IBAN_ACCOUNT_NAME_HOLDER_REMINDER_3("DD Rejected Info IBAN And Account Name Holder Reminder 3"),
    DD_REJECTED_INFO_IBAN_ACCOUNT_NAME_HOLDER_REMINDER_4("DD Rejected Info IBAN And Account Name Holder Reminder 4"),
    DD_REJECTED_INFO_IBAN_ACCOUNT_NAME_HOLDER_REMINDER_5("DD Rejected Info IBAN And Account Name Holder Reminder 5"),

    DD_REJECTED_INFO_ACCOUNT_NAME_HOLDER("DD Rejected Info Account Name Holder"),
    DD_REJECTED_INFO_ACCOUNT_NAME_HOLDER_REMINDER_1("DD Rejected Info Account Name Holder Reminder 1"),
    DD_REJECTED_INFO_ACCOUNT_NAME_HOLDER_REMINDER_2("DD Rejected Info Account Name Holder Reminder 2"),
    DD_REJECTED_INFO_ACCOUNT_NAME_HOLDER_REMINDER_3("DD Rejected Info Account Name Holder Reminder 3"),
    DD_REJECTED_INFO_ACCOUNT_NAME_HOLDER_REMINDER_4("DD Rejected Info Account Name Holder Reminder 4"),
    DD_REJECTED_INFO_ACCOUNT_NAME_HOLDER_REMINDER_5("DD Rejected Info Account Name Holder Reminder 5"),

    DD_REJECTED_INFO_IBAN("DD Rejected Info IBAN"),
    DD_REJECTED_INFO_IBAN_REMINDER_1("DD Rejected Info IBAN Reminder 1"),
    DD_REJECTED_INFO_IBAN_REMINDER_2("DD Rejected Info IBAN Reminder 2"),
    DD_REJECTED_INFO_IBAN_REMINDER_3("DD Rejected Info IBAN Reminder 3"),
    DD_REJECTED_INFO_IBAN_REMINDER_4("DD Rejected Info IBAN Reminder 4"),
    DD_REJECTED_INFO_IBAN_REMINDER_5("DD Rejected Info IBAN Reminder 5"),

    DD_REJECTED_INFO_EID("DD Rejected Info EID"),
    DD_REJECTED_INFO_EID_REMINDER_1("DD Rejected Info EID Reminder 1"),
    DD_REJECTED_INFO_EID_REMINDER_2("DD Rejected Info EID Reminder 2"),
    DD_REJECTED_INFO_EID_REMINDER_3("DD Rejected Info EID Reminder 3"),
    DD_REJECTED_INFO_EID_REMINDER_4("DD Rejected Info EID Reminder 4"),
    DD_REJECTED_INFO_EID_REMINDER_5("DD Rejected Info EID Reminder 5"),



    DD_REJECTED("DD Rejected"),
    CONTRACT_END_REMINDER("Contract End Date");

    private final String label;

    private DDMsgConfigType(String label) {
        this.label = label;
    }

    @Override
    public String getLabel() {
        return label;
    }
}