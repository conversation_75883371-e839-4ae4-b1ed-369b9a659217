package com.magnamedia.service;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.entity.AccountingAPILog;
import com.magnamedia.entity.AccountingLink;
import com.magnamedia.entity.Contract;
import com.magnamedia.repository.AccountingAPILogRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class AccountingAPILogService {
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private AccountingAPILogRepository accountingAPILogRepository;

    private AccountingAPILog createApiLog(Long contractId, String targetClass, String methodName) throws JsonProcessingException {
        HttpServletRequest request = Setup.getCurrentHttpRequest();
        Map<String, String> headers = new HashMap<>();
        if(request != null) {
            Enumeration<String> headersNames = request.getHeaderNames();
            if(headersNames != null) {
                List<String> headersNamesList = Collections.list(headersNames);
                for (String headerName : headersNamesList) {
                    if (headerName != null)
                        headers.put(headerName,
                                headerName.equalsIgnoreCase("authorization") ? "***" :
                                        request.getHeader(headerName));
                }
            }
        }

        AccountingAPILog log = new AccountingAPILog();
        log.setContractId(contractId);
        log.setController(targetClass);
        log.setMethodName(methodName);
        log.setPath(request != null? request.getRequestURI(): "");
        log.setDate(new Date());
        log.setHeaders(objectMapper.writeValueAsString(headers));
        log.setMethod(request != null? request.getMethod() : "");
        log.setModuleName(Setup.getCurrentModule().getCode());
        log.setIpAddress(CurrentRequest.getClientIp());
        log.setData(prepareRequestData());
        log.setUserName(CurrentRequest.getUser() == null ? null : CurrentRequest.getUser().getLoginName());
        return log;
    }

    private String prepareRequestData() {
        HttpServletRequest request = Setup.getCurrentHttpRequest();
        if (request == null)
            return "";
        StringBuilder data = new StringBuilder("");
        Enumeration<String> parameterNames = request.getParameterNames();
        while (parameterNames.hasMoreElements()) {
            String paramName = parameterNames.nextElement();
            String paramValue = request.getParameter(paramName);
            data.append("Parameter: ").append(paramName).append(" = ").append(paramValue).append("\n");
        }

        try {
            String requestBody = request.getReader().lines().collect(Collectors.joining(System.lineSeparator()));
            data.append("Body: " + requestBody);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return data.toString();
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void loggingSignDdByClientApi(
            String contractUUID,
            Contract contract,
            MultipartFile eidPhoto,
            MultipartFile ibanPhoto,
            MultipartFile accountPhoto,
            boolean eidPhotoChanged,
            boolean ibanPhotoChanged,
            boolean accountNamePhotoChanged,
            String eid,
            String iban,
            String account,
            List<MultipartFile> signatures,
            boolean useOldSignature,
            boolean needToSign,
            List<LinkedHashMap> cashPayments,
            boolean signingPaperMode,
            boolean fromCcApp,
            boolean pendingOcr,
            String requestedUrl,
            Long contractId) {

        try {
            Map<String, Object> m = new HashMap<>();
            m.put("contractUUID", contractUUID);
            m.put("contract", contract == null|| contract.getId() == null ? "NULL" : contract.getId());
            m.put("eidPhoto", eidPhoto == null ? "NULL" : "Not NULL");
            m.put("ibanPhoto", ibanPhoto == null ? "NULL" : "Not NULL");
            m.put("accountPhoto", accountPhoto == null ? "NULL" : "Not NULL");
            m.put("eidPhotoChanged", eidPhotoChanged);
            m.put("ibanPhotoChanged", ibanPhotoChanged);
            m.put("accountNamePhotoChanged", accountNamePhotoChanged);
            m.put("eid", eid);
            m.put("iban", iban);
            m.put("account", account);
            m.put("signatures", signatures == null ? "NULL" : ("size: " + signatures.size()));
            m.put("useOldSignature", useOldSignature);
            m.put("needToSign", needToSign);
            m.put("cashPayments", cashPayments == null ? "NULL" : objectMapper.writeValueAsString(cashPayments));
            m.put("signingPaperMode", signingPaperMode);
            m.put("fromCcApp", fromCcApp);
            m.put("pendingOcr", pendingOcr);
            m.put("requestedUrl", requestedUrl);

            AccountingAPILog log = createApiLog(contractId, "ContractPaymentTermController", "signDDByClientApi");
            log.setAdditionalValue("parameters", m);
            accountingAPILogRepository.save(log);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void loggingFlowTerminationMessageInfoByClientApi(AccountingLink accountingLink, Map<String, Object> response) {

        try {
            AccountingAPILog log = createApiLog(accountingLink.getContractId(), "ContractPaymentConfirmationToDoController", "getFlowTerminationMessageInfo");

            Map<String, Object> m = new HashMap<>();
            m.put("accountingLinkId", accountingLink.getId());
            m.put("relatedFlowId", accountingLink.getRelatedFlowId());
            m.put("relatedFlowEntityType", accountingLink.getRelatedFlowEntityType());
            m.put("relatedEntityId", accountingLink.getRelatedEntityId());
            m.put("relatedEntityType", accountingLink.getRelatedEntityType());
            m.put("response", response);
            log.setAdditionalValue("parameters", m);
            accountingAPILogRepository.save(log);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}