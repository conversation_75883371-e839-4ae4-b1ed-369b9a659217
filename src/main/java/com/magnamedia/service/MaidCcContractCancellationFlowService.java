package com.magnamedia.service;


import com.magnamedia.controller.DirectDebitCancelationToDoController;
import com.magnamedia.core.Setup;
import com.magnamedia.entity.Contract;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ContractFeesType;
import com.magnamedia.repository.ContractRepository;
import org.joda.time.DateTime;
import org.joda.time.Hours;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

@Service
public class MaidCcContractCancellationFlowService {
    private final Logger logger = Logger.getLogger(MaidCcContractCancellationFlowService.class.getName());

    @Autowired
    private RefundService refundService;
    @Autowired
    private ClientMessagingAndRefundService clientMessagingAndRefundService;
    @Autowired
    private ContractService contractService;
    
    @Autowired
    private ContractRepository contractRepository;
    
    public Map<String, Object> startNewFlow(Contract contract, Map<String, Object> proratedContractConditions) {
        Map<String, Object> map = new HashMap();
        logger.log(Level.INFO, "Contract id : {0}", contract.getId());
        Contract old = contractRepository.findOne(contract.getId());

        Integer passeHours = null; // ACC-3555
        if (!contract.isCancelledWithinFirstXDays() &&
                contract.getContractFeesType() != null &&
                contract.getContractFeesType().equals(ContractFeesType.NO_ADJUSTED_END_DATE)) {

            if (contract.getDateOfTermination() != null) {
                passeHours = Hours.hoursBetween(new DateTime(contract.getStartOfContract()),
                        new DateTime(contract.getDateOfTermination())).getHours();

            } else if (contract.getScheduledDateOfTermination() != null) {
                passeHours = Hours.hoursBetween(new DateTime(contract.getStartOfContract()),
                                new DateTime(contract.getScheduledDateOfTermination())).getHours();
            }

            logger.info("Passed hours: " + passeHours);

            if (passeHours != null &&
                    (passeHours < (Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                            AccountingModule.PARAMETER_CC_CONTRACT_CHARGE_FREE_DAYS)) * 24))) {

                map.putAll(refundService.doWithinXDaysCancellationFlow(contract, false));
                return map;
            }
        }

        Double correctedBalance = (Double) proratedContractConditions.get("correctedBalance");
        boolean returnedMaidPreviousMonth = (boolean) proratedContractConditions.get("returnedMaidPreviousMonth");
        boolean clientCancelledWithinXDays = (boolean) proratedContractConditions.get("clientCancelledWithinXDays");

        if (clientCancelledWithinXDays || returnedMaidPreviousMonth) {
            boolean oweRefundToClient = correctedBalance != null && correctedBalance <= -1D;
            logger.info("oweRefundToClient: " + oweRefundToClient);

            clientMessagingAndRefundService.sendProratedContractSMSToClient(
                    old, clientCancelledWithinXDays, returnedMaidPreviousMonth, oweRefundToClient);

            if(!contract.isCancelledWithinFirstXDays() && oweRefundToClient) {
                refundService.doWithinXDaysCancellationFlow(contract, true);
                map.put("settled", true);
                return map;
            }
        }

        // if we want money from client -> do nothing
        if (correctedBalance != null && correctedBalance >= 1D) {
            logger.log(Level.SEVERE, "client balance >= 1 -> do nothing");
            return map;
        }

        // we owes refund to the client
        if (correctedBalance != null && correctedBalance <= -1D && !old.getSettled()) {
            map.putAll(refundService.doCancellationRefundFlow(contract, old.getClient()));
        }

        // if contract is not a special case -> cancel future dds
        Setup.getApplicationContext().getBean(DirectDebitCancelationToDoController.class)
                .cancelAllContractDDs(contract);

        return map;
    }
}
