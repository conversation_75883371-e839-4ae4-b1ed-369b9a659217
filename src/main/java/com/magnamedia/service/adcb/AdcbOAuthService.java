package com.magnamedia.service.adcb;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.http.converter.FormHttpMessageConverter;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Service for handling ADCB OAuth token authentication
 * 
 * <AUTHOR>
 * Created on Dec 2024
 */
@Service
public class AdcbOAuthService {
    
    private static final Logger logger = Logger.getLogger(AdcbOAuthService.class.getName());
    
    private static final String TOKEN_URL = "https://devmag.adcb.com/auth/oauth/v2/token";
    private static final String GRANT_TYPE = "client_credentials";
    private static final String SCOPE = "CorporateOwnFTBulk CorporateAdhocFTBulk CorporateEnquireFTBulk CorporateAccountStatement DDSMandateManage DDSMandateEnq DDSCollection DDSCollectionEnq DDSFileUpload";
    public static String token;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * Get OAuth access token with custom credentials
     *
     * @param clientId The client ID
     * @param clientSecret The client secret
     * @return OAuth token response
     */
    public String getAccessToken(String clientId, String clientSecret) {
        try {
            logger.log(Level.INFO, "Requesting OAuth token from ADCB API with custom credentials");

            if (clientId == null || clientId.isEmpty() || clientSecret == null || clientSecret.isEmpty()) {
                logger.info("The token is already being generated -> exist from getAccessToken");
                return token;
            }

            RestTemplate restTemplate = createRestTemplate();
            
            // Create Basic Auth header
            String credentials = clientId + ":" + clientSecret;
            String encodedCredentials = Base64.getEncoder().encodeToString(credentials.getBytes());
            
            // Prepare headers
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            headers.set("Authorization", "Basic " + encodedCredentials);
            
            // Prepare form data
            MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
            formData.add("grant_type", GRANT_TYPE);
            formData.add("scope", SCOPE);
            
            // Create request entity
            HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(formData, headers);

            ResponseEntity<Map> response = restTemplate.exchange(
                    TOKEN_URL,
                    HttpMethod.POST,
                    requestEntity,
                    Map.class);

            logger.info("response status : " + response.getStatusCode());
            logger.info("response body : " + response.getBody());

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                logger.info("Successfully obtained OAuth token from ADCB API with custom credentials");
                token = (String) response.getBody().get("access_token");
                return token;
            } else {
                logger.severe("Failed to obtain OAuth token. Status: " + response.getStatusCode());
                throw new RuntimeException("Failed to obtain OAuth token from ADCB API");
            }
            
        } catch (Exception e) {
            logger.severe("Error obtaining OAuth token from ADCB API with custom credentials : " + e);
            throw new RuntimeException("Error obtaining OAuth token from ADCB API", e);
        }
    }
    
    /**
     * Create RestTemplate with proper configuration
     */
    private RestTemplate createRestTemplate() {
        RestTemplate template = new RestTemplate();
        
        // Configure message converters
        List<HttpMessageConverter<?>> messageConverters = new ArrayList<>();
        
        // Add JSON converter
        MappingJackson2HttpMessageConverter jsonConverter =
            new MappingJackson2HttpMessageConverter();
        jsonConverter.setObjectMapper(objectMapper);
        messageConverters.add(jsonConverter);
        
        // Add form converter for application/x-www-form-urlencoded
        FormHttpMessageConverter formConverter =
            new FormHttpMessageConverter();
        messageConverters.add(formConverter);
        
        // Add string converter
        StringHttpMessageConverter stringConverter =
            new StringHttpMessageConverter();
        messageConverters.add(stringConverter);
        
        template.setMessageConverters(messageConverters);
        
        return template;
    }
} 