package com.magnamedia.service.adcb;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Collections;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Service for making authenticated API calls to ADCB using OAuth tokens
 * 
 * <AUTHOR>
 * Created on Dec 2024
 */
@Service
public class AdcbApiService {
    
    private static final Logger logger = Logger.getLogger(AdcbApiService.class.getName());
    
    // Base URL for ADCB API endpoints
    private static final String ADCB_BASE_URL = "https://devmag.adcb.com/";
    
    @Autowired
    private AdcbOAuthService adcbOAuthService;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * Make a POST request to ADCB API with OAuth authentication
     * 
     * @param endpoint The API endpoint (without base URL)
     * @param body The request body object
     * @return API response
     */
    /*public Map postWithOAuth(String endpoint, Map<String, Object> body) {
        try {
            logger.log(Level.INFO, "Making POST request to ADCB API endpoint: " + endpoint);
            
            // Get OAuth token
            AdcbOAuthService.AdcbTokenResponse tokenResponse = adcbOAuthService.getAccessToken();
            String bearerToken = tokenResponse.getToken_type() + " " + tokenResponse.getAccess_token();
            
            // Create headers
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", bearerToken);
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Accept", MediaType.APPLICATION_JSON_VALUE);
            
            // Create request entity
            HttpEntity<Map> requestEntity = new HttpEntity<>(body, headers);
            
            // Make the request
            RestTemplate restTemplate = createRestTemplate();
            String fullUrl = ADCB_BASE_URL + endpoint;
            
            ResponseEntity<Map> response = restTemplate.exchange(
                fullUrl,
                HttpMethod.POST,
                requestEntity,
                Map.class
            );
            
            if (response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.CREATED) {
                logger.log(Level.INFO, "Successfully made POST request to ADCB API: " + endpoint);
                return response.getBody();
            } else {
                logger.log(Level.SEVERE, "Failed to make POST request. Status: " + response.getStatusCode());
                throw new RuntimeException("Failed to make POST request to ADCB API. Status: " + response.getStatusCode());
            }
            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error making POST request to ADCB API: " + endpoint, e);
            throw new RuntimeException("Error making POST request to ADCB API", e);
        }
    }*/
    
    /**
     * Make a POST request to ADCB API with custom OAuth credentials
     *
     * @param body The request body object
     * @param clientId The client ID
     * @param clientSecret The client secret
     * @return API response
     */
    public Map postWithCustomOAuth(Map<String, Object> body, String clientId, String clientSecret) {
        try {
            logger.info("Making POST request to ADCB API endpoint with custom credentials: " + body.get("fullUrl"));

            HttpMethod method = body.containsKey("method")
                    ? HttpMethod.resolve((String) body.get("method"))
                    : HttpMethod.POST;

            // Build URI with query parameters
            String fullUrl = body.get("fullUrl").toString();
            UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(fullUrl);

            if (body.containsKey("queryParams") && body.get("queryParams") != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> queryParams = (Map<String, Object>) body.get("queryParams");
                queryParams.forEach((key, value) -> {
                    if (value != null) {
                        uriBuilder.queryParam(key, value);
                    }
                });
            }

            // Create headers
            HttpHeaders headers = new HttpHeaders();

            // Get OAuth token with custom credentials
            if (!body.containsKey("headers") ||
                    !((Map<String, Object>) body.get("headers")).containsKey("Authorization")) {
                String bearerToken = "Bearer " + adcbOAuthService.getAccessToken(clientId, clientSecret);
                logger.info("bearer token : " + bearerToken);

                headers.set("Authorization", bearerToken);
            }
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Accept", MediaType.APPLICATION_JSON_VALUE);

            if (body.containsKey("headers") && body.get("headers") != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> headersMap = (Map<String, Object>) body.get("headers");
                headersMap.forEach((key, value) -> {
                    if (value != null) {
                        headers.set(key, (String) value);
                    }
                });
            }

            String finalUrl = uriBuilder.toUriString();
            logger.info("final Url : " + finalUrl);

            // Create request entity
            HttpEntity<?> requestEntity;
            Object requestBody = body.getOrDefault("body", null);

            if (requestBody != null) {
                requestEntity = new HttpEntity<>(objectMapper.convertValue(requestBody, Map.class), headers);
            } else if (body.containsKey("passAllBody")) {
                requestEntity = new HttpEntity<>(body, headers);
            } else {
                requestEntity = new HttpEntity<>(headers);
            }
            
            // Make the request
            RestTemplate restTemplate = createRestTemplate();

            ResponseEntity<Map> response = restTemplate.exchange(
                finalUrl,
                method,
                requestEntity,
                Map.class);

            logger.info("response status : " + response.getStatusCode());
            logger.info("response body : " + response.getBody());
            
            if (response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.CREATED) {
                logger.log(Level.INFO, "Successfully made POST request to ADCB API with custom credentials: " + finalUrl);
                return response.getBody();
            } else {
                logger.log(Level.SEVERE, "Failed to make POST request. Status: " + response.getStatusCode());
                throw new RuntimeException("Failed to make POST request to ADCB API. Status: " + response.getStatusCode());
            }
            
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Error making POST request to ADCB API with custom credentials: " + body.get("finalUrl"), e);
            throw new RuntimeException("Error making POST request to ADCB API", e);
        }
    }

    /**
     * Create RestTemplate with proper configuration
     */
    private RestTemplate createRestTemplate() {
        RestTemplate template = new RestTemplate();
        
        // Configure message converters
        org.springframework.http.converter.json.MappingJackson2HttpMessageConverter converter = 
            new org.springframework.http.converter.json.MappingJackson2HttpMessageConverter();
        converter.setObjectMapper(objectMapper);
        template.setMessageConverters(Collections.singletonList(converter));
        
        return template;
    }
}