package com.magnamedia.helper;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.paytabs.Paytabs;
import com.magnamedia.core.helper.paytabs.PaytabsException;
import com.magnamedia.core.helper.paytabs.PaytabsTransaction;
import com.magnamedia.core.helper.paytabs.PaytabsTransactionInfo;
import com.magnamedia.core.type.CoreParameter;
import com.magnamedia.extra.StringUtils;
import com.magnamedia.module.type.PaytabsTransactionStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;

/**
 * Created by Mamon.Masod on 7/4/2021.
 */
@Component
public class PayTabsHelper {
    public static final String PAYTABS_CART_ID_SEPARATOR = "____";

    @Autowired
    private Paytabs paytabs;
    @Autowired
    private ObjectMapper mapper;

    public PaytabsTransaction generatePayTabsLink(String identifier, String description, Double amount, String relativeRedirectLink) {
        try {
            return paytabs.createTransaction(
                    // adding timestamp to avoid duplicate request error when generate a new link
                    identifier + PAYTABS_CART_ID_SEPARATOR + System.nanoTime(),
                    description,
                    amount,
                    Setup.getCoreParameter(CoreParameter.PAYTABS_DEFAULT_CURRENCY),
                    "sale",
                    "ecom",
                    Setup.getCoreParameter(CoreParameter.BASE_URL) + relativeRedirectLink
            );
        } catch (IOException | PaytabsException e) {
            e.printStackTrace();
            throw new RuntimeException("something went wrong with the payment link");
        }
    }

    public PaytabsTransactionStatus checkPaytabsTransaction(String creditCardInfo) {
        PaytabsTransactionInfo transactionInfo;
        try {
            transactionInfo = getPayTabsTransactionInfo(creditCardInfo);
        } catch (PaytabsException e) {
            return PaytabsTransactionStatus.PENDING;
        }
        return transactionInfo.getPayment_result().getResponse_status().equals("A") ?
                PaytabsTransactionStatus.PAID : PaytabsTransactionStatus.FAILED;
    }

    public PaytabsTransactionInfo getPayTabsTransactionInfo(String creditCardInfo) throws PaytabsException {
        try {
            String transactionRef = null;
            if (!StringUtils.isEmpty(creditCardInfo)) {
                transactionRef = mapper.readValue(creditCardInfo, PaytabsTransaction.class)
                        .getTran_ref();
            }
            if (transactionRef != null) {
                return paytabs.getTransactionInfo(transactionRef);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return null;
    }

    public String extractUUID(Map<String, Object> body) {
        String entityUUID = body.get("cartId").toString().split(PAYTABS_CART_ID_SEPARATOR)[0];
        return entityUUID;
    }

    public String convertToString(Object paytabsTransaction) {
        try {
            return mapper.writeValueAsString(paytabsTransaction);
        } catch (IOException e) {
            throw new RuntimeException("something went wrong with the while parsing");
        }
    }
}
