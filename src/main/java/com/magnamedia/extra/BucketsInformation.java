/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.magnamedia.extra;

/**
 *
 * <AUTHOR>
 */
public interface BucketsInformation {

//    public Long ID;
//    public String CODE;
//    public Double TOTALSUM;
//    public Double TOTALMINUS;
//
//    public Long getID() {
//        return ID;
//    }
//
//    public void setID(Long ID) {
//        this.ID = ID;
//    }
//
//    public String getCODE() {
//        return CODE;
//    }
//
//    public void setCODE(String CODE) {
//        this.CODE = CODE;
//    }
//
//    public Double getTOTALSUM() {
//        return TOTALSUM;
//    }
//
//    public void setTOTALSUM(Double TOTALSUM) {
//        this.TOTALSUM = TOTALSUM;
//    }
//
//    public Double getTOTALMINUS() {
//        return TOTALMINUS;
//    }
    public String getCODE();

    public Long getID();

    public Double getTOTALSUM();

    public Double getTOTALMINUS();

//    public void setTOTALMINUS(Double TOTALMINUS) {
//        this.TOTALMINUS = TOTALMINUS;
//    }
//    public BucketsInformation(Long ID,
//            String CODE,
//            Double TOTALSUM,
//            Double TOTALMINUS) {
//        this.ID = ID;
//        this.CODE = CODE;
//        this.TOTALSUM = TOTALSUM;
//        this.TOTALMINUS = TOTALMINUS;
//    }
}
