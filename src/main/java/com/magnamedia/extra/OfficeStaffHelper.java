package com.magnamedia.extra;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.User;
import com.magnamedia.entity.OfficeStaff;
import com.magnamedia.repository.AccountingUserRepository;
import com.magnamedia.repository.OfficeStaffRepository;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 * author Basel Mariam.
 */
public class OfficeStaffHelper {
    private static final Logger logger = Logger.getLogger(OfficeStaffHelper.class.getName());

    public static Set<Long> getEmployeeHierarchyTree(OfficeStaff manager) {
        Set<Long> userIds = new HashSet<>();
        Set<Long> officeStaffIds = new HashSet<>();
        officeStaffIds.add(manager.getId());
        OfficeStaffRepository officeStaffRepository = Setup.getRepository(OfficeStaffRepository.class);
        AccountingUserRepository userRepository = Setup.getRepository(AccountingUserRepository.class);

        List<OfficeStaff> officeStaffs = officeStaffRepository.findEmployeesByManager(
                new HashSet<>(Collections.singleton(manager.getId())), officeStaffIds);

        do {
            for (OfficeStaff employee : officeStaffs) {
                logger.info("Sub Employee Id:" + employee.getId());

                if (employee.getEmail() == null || employee.getEmail().isEmpty()) continue;

                List<User> users = userRepository.findLinkedUserByEmail(employee.getEmail());
                if (!users.isEmpty()) {
                    userIds.add(users.get(0).getId());
                }
            }

            if (!officeStaffs.isEmpty()) {
                Set<Long> employeeIds = officeStaffs.stream()
                        .map(OfficeStaff::getId)
                        .collect(Collectors.toSet());
                officeStaffIds.addAll(employeeIds);
                logger.info("Employees to check: " + employeeIds +
                        "; Employees to avoid: " + officeStaffIds);

                officeStaffs = officeStaffRepository.findEmployeesByManager(employeeIds, officeStaffIds);
            }

        } while (!officeStaffs.isEmpty());

        return userIds;
    }
}