package com.magnamedia.extra;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.ContractCancellationLog;
import com.magnamedia.entity.Payment;
import com.magnamedia.entity.serializer.IdLabelListSerializer;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ContractTerminationHelper {

    private Double weShouldRefund;
    private Double clientOwes;
    @JsonSerialize(using = IdLabelListSerializer.class)
    private List<Payment> payments;
    private ContractCancellationLog cancellationLog;

    public ContractTerminationHelper(Double weShouldRefund, Double clientOwes, List<Payment> payments) {
        this.weShouldRefund = weShouldRefund;
        this.clientOwes = clientOwes;
        this.payments = payments;
    }

    public ContractTerminationHelper(Double weShouldRefund, Double clientOwes, List<Payment> payments, ContractCancellationLog cancellationLog, Contract contract) {
        this.weShouldRefund = weShouldRefund;
        this.clientOwes = clientOwes;
        this.payments = payments;
        this.cancellationLog = cancellationLog;
    }

    public ContractTerminationHelper() {
    }
    
    

    public Double getWeShouldRefund() {
        return weShouldRefund;
    }

    public void setWeShouldRefund(Double weShouldRefund) {
        this.weShouldRefund = weShouldRefund;
    }

    public Double getClientOwes() {
        return clientOwes;
    }

    public void setClientOwes(Double clientOwes) {
        this.clientOwes = clientOwes;
    }

    public List<Payment> getPayments() {
        return payments;
    }

    public void setPayments(List<Payment> payments) {
        this.payments = payments;
    }

    public ContractCancellationLog getCancellationLog() {
        return cancellationLog;
    }

    public void setCancellationLog(ContractCancellationLog cancellationLog) {
        this.cancellationLog = cancellationLog;
    }

    public ContractTerminationHelper(Double weShouldRefund, Double clientOwes, List<Payment> payments, ContractCancellationLog cancellationLog) {
        this.weShouldRefund = weShouldRefund;
        this.clientOwes = clientOwes;
        this.payments = payments;
        this.cancellationLog = cancellationLog;
    }

}
