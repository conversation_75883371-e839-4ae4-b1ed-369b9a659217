package com.magnamedia.extra;

import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.workflow.Task;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.rest.core.config.Projection;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 **/

@Projection(name = "onlineCardStatementFileProjection",
        types = BaseEntity.class)
public interface OnlineCardStatementFileProjection
        extends Task {

    Long getId();

    Date getUploadedDate();

    boolean isResolved();

    boolean isDeleted();

    int getTotalTransactions();

    int getTotalMatchedPayments();

    int getTotalUnMatchedPayments();

    int getTotalRefundedPayments();
    int getTotalMatchedRefunds();

    int getTotalUnmatchedRefunds();

    @Value("#{{id: target.getAttachments()?.get(0).getId(), " +
            "uuid: target.getAttachments()?.get(0).getUuid(), " +
            "name: target.getAttachments()?.get(0).getName()}}")
    Map<?,?> getAttachment();
}
