package com.magnamedia.extra;


import com.magnamedia.core.Setup;
import com.magnamedia.entity.AbstractPaymentTypeConfig;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.entity.ContractPaymentType;
import com.magnamedia.module.AccountingModule;
import org.joda.time.DateTime;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @created 30/01/2025 - 2:33 PM
 * ACC-8721
 */
public class DiscountsWithVatHelper {

    public static double getVatPercent() {
        return Double.parseDouble(Setup.getParameter(
                Setup.getModule(AccountingModule.SALES_MODULE_CODE), AccountingModule.PARAMETER_CONTRACT_VAT_PERCENT)) / 100;
    }

    public static Double getVatAmount(double amount) { return getVatAmount(amount, getVatPercent()); }

    public static Double getVatAmount(double amount, double vat) { return amount * vat; }

    public static Double getAmountPlusVat(double amount, double vatPercent, double workerSalary, boolean isWorkerSalaryVatted) {
        return isWorkerSalaryVatted ?
                getAmountPlusVat(amount, vatPercent) :
                getAmountPlusVat(amount - workerSalary, vatPercent) + workerSalary;
    }

    public static Double getAmountPlusVat(double amount) { return getAmountPlusVat(amount, getVatPercent()); }

    public static Double getAmountPlusVat(double amount, double vatPercent) { return amount * (1 + vatPercent); }

    public static Double getAmountWithoutVat(boolean isMaidVisa, boolean isWorkerSalaryVatted, boolean includeWorkerSalary, Double workerSalary, double amount) {

        return isMaidVisa && includeWorkerSalary && !isWorkerSalaryVatted ?
                workerSalary + DiscountsWithVatHelper.getAmountWithoutVat(amount - workerSalary) :
                DiscountsWithVatHelper.getAmountWithoutVat(amount);
    }

    public static Double getAmountWithoutVat(double amount) { return getAmountWithoutVat(amount, getVatPercent()); }

    public static Double getAmountWithoutVat(double amount, double vatPercent) { return amount / (1 + vatPercent); }

    public static ContractPaymentType getAddOnPaymentType(Date paymentDate, List<ContractPaymentType> l, Contract contract){

        return l.stream()
                .filter(addOnPaymentType -> addOnPaymentType.getType()
                        .getCode().equals(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_ADD_ON_TYPE_CODE) &&
                        addOnPaymentType.getStartsOn() != null &&
                        new DateTime(paymentDate).isAfter(new DateTime(contract.getStartOfContract())
                                .plusMonths(addOnPaymentType.getStartsOn()).dayOfMonth().withMinimumValue().withTimeAtStartOfDay().minusHours(1)) &&
                        (addOnPaymentType.getEndsAfter() == null || new DateTime(paymentDate).isBefore(
                                new DateTime(contract.getStartOfContract())
                                        .plusMonths(addOnPaymentType.getEndsAfter()).dayOfMonth().withMinimumValue().withTimeAtStartOfDay())))
                .findFirst().
                orElse(null);
    }

    public static ContractPaymentType getContractPaymentTypeWithAdditionalDiscount(ContractPaymentTerm cpt) {
        if (cpt.getContractPaymentTypes() == null || cpt.getContractPaymentTypes().isEmpty()) return null;

        return cpt.getContractPaymentTypes().stream()
                .filter(p -> p.getAffectedByAdditionalDiscount() != null && p.getAffectedByAdditionalDiscount())
                .findFirst().orElse(null);
    }

    public static Double getFinalAmountAfterAddDiscount(double amount, double discount) {
        return getFinalAmountAfterAddDiscount(amount, discount, getVatPercent());
    }

    public static Double getFinalAmountAfterAddDiscount(double amount, double discount, double vat) {
        return amount - discount - getVatAmount(discount, vat);
    }
}
