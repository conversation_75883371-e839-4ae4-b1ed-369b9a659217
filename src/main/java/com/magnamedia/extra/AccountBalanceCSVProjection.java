package com.magnamedia.extra;

import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.workflow.Task;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.rest.core.config.Projection;

import java.util.Date;

@Projection(name = "AccountBalanceCSVProjection", types = BaseEntity.class)
public interface AccountBalanceCSVProjection extends Task {

    Long getId();

    @Value("#{target.getFromBucket() != null ? target.getFromBucket().getName() : ''}")
    String getFromBucket();

    @Value("#{target.getRevenue() != null ? target.getRevenue().getName() : ''}")
    String getRevenue();

    @Value("#{target.getExpense() != null ? target.getExpense().getName() : '' }")
    String getExpense();

    @Value("#{target.getToBucket() != null ? target.getToBucket().getName() : ''}")
    String getToBucket();

    @Value("#{target.getDescription() != null ? target.getDescription : ''}")
    String getDescription();

    Double getAmount();

    Date getDate();

    Date getCreationDate();

    String getBucketPreBalance();

    String getBucketBalance();
}