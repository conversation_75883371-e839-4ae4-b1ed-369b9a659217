/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.magnamedia.extra;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.magnamedia.entity.CreditCardStatement;
import java.util.List;

/**
 *
 * <AUTHOR> <<EMAIL>>
 */
public class CreditCardStatementHelper {
     private List<StatementCand> statements;
     
     
     private Integer importedLines;
     
     private Integer erroneousLines;
     
     private Integer duplicatedLines;
     
     private Integer newLines;
     
     private Integer csvLines;

     
    public List<StatementCand> getStatements() {
        return statements;
    }

    public void setStatements(List<StatementCand> statements) {
        this.statements = statements;
    }


    public Integer getImportedLines() {
        return statements.size();
    }

    public void setImportedLines(Integer importedLines) {
        this.importedLines = importedLines;
    }

    public Integer getErroneousLines() {
        erroneousLines=0;
        for(StatementCand temp: statements){
            if(!temp.getIsValid())
              erroneousLines++;  
        }
        return erroneousLines;
    }

    public void setErroneousLines(Integer erroneousLines) {
        this.erroneousLines = erroneousLines;
    }

    public Integer getDuplicatedLines() {
        duplicatedLines=0;
        for(StatementCand temp: statements){
            if(temp.getIsDuplicated())
              duplicatedLines++;  
        }
        return duplicatedLines;
    }

    public void setDuplicatedLines(Integer duplicatedLines) {
        this.duplicatedLines = duplicatedLines;
    }

    public Integer getNewLines() {
        newLines=0;
        for(StatementCand temp: statements){
            if(!temp.getIsDuplicated())
              newLines++;  
        }
        return newLines;
    }

    public void setNewLines(Integer newLines) {
        this.newLines = newLines;
    }

    public Integer getCsvLines() {
        return csvLines;
    }

    public void setCsvLines(Integer csvLines) {
        this.csvLines = csvLines;
    }
     
     
     
}
